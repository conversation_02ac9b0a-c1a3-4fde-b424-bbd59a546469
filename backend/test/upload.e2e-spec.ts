import { Test, TestingModule } from '@nestjs/testing';
import { StorageService } from '../src/storage/storage.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as mime from 'mime-types';


describe('StorageService Upload (E2E)', () => {
  let storageService: StorageService;
  let configService: ConfigService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env', // Make sure you have a .env file with your variables
        }),
      ],
      providers: [StorageService],
    }).compile();

    storageService = module.get<StorageService>(StorageService);
    configService = module.get<ConfigService>(ConfigService);

    const bucketName = configService.get('GOOGLE_CLOUD_STORAGE_BUCKET');
    if (!bucketName) {
      throw new Error('GOOGLE_CLOUD_STORAGE_BUCKET is not defined in the environment variables. Please check your .env file or environment configuration.');
    }
  });

  it('should generate a signed URL and upload a specific image', async () => {
    // 1. Define file details
    const imagePath = path.resolve(
      __dirname,
      '../../ChroniCare/assets/images/Picture1.jpg',
    );
    
    const contentType = mime.lookup(imagePath) || 'application/octet-stream';
    const objectPath = `test-uploads/Picture1-test-${Date.now()}.jpg`;

    // Verify the image file exists
    expect(fs.existsSync(imagePath)).toBe(true);

    // 2. Generate signed URL
    const signedUrl = await storageService.generateUploadUrl(
      objectPath,
      contentType,
    );
    expect(signedUrl).toBeDefined();
    expect(typeof signedUrl).toBe('string');

    // 3. Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // 4. Upload the file using the signed URL (GCS uses PUT for upload)
    let uploadResponse;
    try {
      uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: imageBuffer,
        headers: {
          'Content-Type': contentType,
        },
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
    
    // 5. Assert the upload was successful
    expect(uploadResponse.status).toBe(200);

    // Optional: Verify the public URL
    const publicUrl = storageService.getPublicUrl(objectPath);
    console.log('File uploaded successfully. Public URL:', publicUrl);
  });
}); 