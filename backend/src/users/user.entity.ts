import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class CommunityInfo {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field(() => String, { nullable: true })
  displayName?: string | null;
}

@ObjectType()
export class User {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  firebaseUid: string;

  @Field(() => String)
  email: string;

  @Field(() => String, { nullable: true })
  displayName?: string | null;

  @Field(() => String, { nullable: true })
  firstName?: string | null;

  @Field(() => String, { nullable: true })
  lastName?: string | null;

  @Field(() => Boolean)
  emailVerified: boolean;

  @Field(() => String, { nullable: true })
  phoneNumber?: string | null;

  @Field(() => String, { nullable: true })
  photoURL?: string | null;

  @Field(() => [CommunityInfo], { nullable: true })
  communities?: CommunityInfo[];

  @Field(() => Boolean)
  onboardingCompleted: boolean;

  @Field(() => UserConsent, { nullable: true })
  consent?: UserConsent | null;

  @Field(() => Date, { nullable: true })
  creationTime?: Date | null;

  @Field(() => Date, { nullable: true })
  lastSignInTime?: Date | null;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  condition?: string;

  @Field(() => String, { nullable: true })
  userType?: string;
}

@ObjectType()
export class UserConsent {
  @Field(() => ID)
  id: string;

  @Field(() => Boolean)
  consentDataPrivacy: boolean;

  @Field(() => Date, { nullable: true })
  consentDataPrivacyAt?: Date | null;

  @Field(() => Boolean)
  consentTermsOfService: boolean;

  @Field(() => Date, { nullable: true })
  consentTermsOfServiceAt?: Date | null;

  @Field(() => Boolean)
  consentMarketing: boolean;

  @Field(() => Date, { nullable: true })
  consentMarketingAt?: Date | null;
}

@ObjectType()
export class UserPseudonym {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  userId: string;

  @Field(() => String)
  pseudonymId: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
} 