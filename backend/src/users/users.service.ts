import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User, UserPseudonym } from './user.entity';
import { CreateUserInput } from './dto/create-user.input';
import * as admin from 'firebase-admin';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}
  private readonly logger = new Logger(UsersService.name);

  async checkUserExistsByEmail(email: string): Promise<boolean> {
    // It uses Prisma to query the 'users' table in the database
    const user = await this.prisma.client.users.findUnique({ 
      where: { email }, // Looks for a unique user by their email
      select: { id: true }, // Optimization: only fetch the 'id' if the user exists
    });
    return !!user; // Converts the result (user object or null) to a boolean
  }

  async findAll(): Promise<User[]> {
    this.logger.log('Finding all users');
    return this.prisma.client.users.findMany(); 
  }
 
  async findOne(id: string): Promise<User | null> {
    this.logger.log('Finding user by id:', id);
    return this.prisma.client.users.findUnique({
      where: { id },
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    this.logger.log('Finding user by email:', email);
    return this.prisma.client.users.findUnique({
      where: { email },
    });
  }

  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    this.logger.log('Finding user by firebase uid:', firebaseUid);
    return this.prisma.client.users.findUnique({
      where: { firebaseUid },
    });
  }

  async upsertUser(input: CreateUserInput): Promise<User> {
    this.logger.log('Upserting user with input:', input);
    
    const userData = {
      firebaseUid: input.firebaseUid,
      email: input.email,
      displayName: input.displayName || null,
      emailVerified: input.emailVerified ?? false,
      photoURL: input.photoURL || null,
      onboardingCompleted: input.onboardingCompleted ?? false,
      lastSignInTime: new Date(),
    };

    try {
      const version = process.env.CONSENT_VERSION || 'v1.0';
      
      const user = await this.prisma.client.users.upsert({
        where: { firebaseUid: input.firebaseUid },
        update: {
          ...userData,
          updatedAt: new Date(),
        },
        create: {
          ...userData,
          creationTime: new Date(),
          consents: {
            create: [
              {
                type: 'TERMS_OF_SERVICE',
                version,
                granted: true,
                timestamp: new Date(),
              },
            ],
          },
        },
      });

      this.logger.log('User upserted successfully:', user.firebaseUid);
      
      // Create pseudonym if this is a new user
      await this.getOrCreatePseudonym(user.id);
      
      return user;
    } catch (error) {
      this.logger.error('Error upserting user:', error);
      throw error;
    }
  }

  async createFromFirebase(firebaseUser: admin.auth.DecodedIdToken): Promise<User> {
    this.logger.log('Creating user from Firebase:', firebaseUser);
    const version = process.env.CONSENT_VERSION || 'v1.0';
    
    const newUser = await this.prisma.client.users.create({
      data: {
        firebaseUid: firebaseUser.uid,
        email: firebaseUser.email || '',
        displayName: firebaseUser.name || null,
        emailVerified: firebaseUser.email_verified || false,
        photoURL: firebaseUser.picture || null,
        onboardingCompleted: false,
        creationTime: firebaseUser.auth_time ? new Date(firebaseUser.auth_time * 1000) : null,
        lastSignInTime: new Date(), // Current time as last sign in
        consents: {
          create: [
            {
              type: 'TERMS_OF_SERVICE',
              version,
              granted: true,
              timestamp: new Date(),
            },
          ],
        },
      },
    });

    // Create a pseudonym for the new user to ensure data consistency
    await this.getOrCreatePseudonym(newUser.id);
    
    return newUser;
  }

  async updateFromFirebase(id: string, firebaseUser: admin.auth.DecodedIdToken): Promise<User> {
    this.logger.log('Updating user from Firebase:', firebaseUser);
    return this.prisma.client.users.update({
      where: { id },
      data: {
        displayName: firebaseUser.name || null,
        emailVerified: firebaseUser.email_verified || false,
        photoURL: firebaseUser.picture || null,
        lastSignInTime: new Date(), // Update last sign in time
      },
    });
  }

  async updateFirebaseUid(id: string, firebaseUid: string): Promise<User> {
    return this.prisma.client.users.update({
      where: { id },
      data: { firebaseUid },
    });
  }

  async delete(id: string): Promise<User> {
    return this.prisma.client.users.delete({
      where: { id },
    });
  }

  async findConsentByUserId(userId: string) {
    return this.prisma.client.user_consents.findMany({
      where: { userId },
    });
  }

  async updateProfileImageUrls(
    userId: string,
    profileImageUrl: string,
  ): Promise<User> {
    this.logger.log('Updating profile image URL for user:', userId);
    return this.prisma.client.users.update({
      where: { id: userId },
      data: {
        photoURL: profileImageUrl || null, // Convert empty string to null
        updatedAt: new Date(),
      },
    });
  }

  async findUserCommunities(userId: string): Promise<{ id: string; name: string }[]> {
    const pseudonym = await this.findUserPseudonym(userId);

    if (!pseudonym) {
      this.logger.log(`No pseudonym found for user: ${userId}`);
      return [];
    }

    const memberships = await this.prisma.client.communityMembership.findMany({
      where: { user_healthID: pseudonym.pseudonymId },
      include: {
        community: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
      },
    });

    return memberships.map((m) => m.community);
  }

  // Vault operations for pseudonym management
  async createUserPseudonym(userId: string): Promise<UserPseudonym> {
    this.logger.log('Creating user pseudonym for user:', userId);
    return this.prisma.client.user_pseudonyms.create({
      data: {
        userId,
      },
    });
  }

  async findUserPseudonym(userId: string): Promise<UserPseudonym | null> {
    return this.prisma.client.user_pseudonyms.findUnique({
      where: { userId },
    });
  }

  async findByPseudonymId(pseudonymId: string): Promise<string | null> {
    const pseudonym = await this.prisma.client.user_pseudonyms.findUnique({
      where: { pseudonymId },
      select: {
        userId: true
      }
    });
    return pseudonym?.userId || null;
  }

  async getOrCreatePseudonym(userId: string): Promise<UserPseudonym> {
    // Try to find existing pseudonym
    let pseudonym = await this.findUserPseudonym(userId);
    
    // If not found, create a new one
    if (!pseudonym) {
      pseudonym = await this.createUserPseudonym(userId);
    }
    
    return pseudonym;
  }

  async findUserDiseaseProfile(userId: string): Promise<{ condition: string | null; userType: string | null }> {
    const pseudonym = await this.findUserPseudonym(userId);
    if (!pseudonym) {
      return { condition: null, userType: null };
    }
  
    const userDiseaseProfile = await this.prisma.client.userDiseaseProfile.findFirst({
      where: { user_healthID: pseudonym.pseudonymId },
      include: {
        disease: {
          select: {
            displayName: true,
          },
        },
      },
    });
  
    if (!userDiseaseProfile) {
      return { condition: null, userType: null };
    }
  
    const formatUserType = (role: string) => {
      if (!role) return null;
      const lower = role.toLowerCase();
      return lower.charAt(0).toUpperCase() + lower.slice(1);
    };
  
    return {
      condition: userDiseaseProfile.disease.displayName,
      userType: formatUserType(userDiseaseProfile.userRole),
    };
  }
}