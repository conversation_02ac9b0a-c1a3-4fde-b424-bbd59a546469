import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseService implements OnModuleInit {
  private readonly logger = new Logger(FirebaseService.name);

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      // Check if Firebase is already initialized
      if (!admin.apps.length) {
        // Get Firebase project ID from environment variables
        const firebaseProjectId = this.configService.get('FIREBASE_PROJECT_ID', 'chronicare-454115');
        
        admin.initializeApp({
          // Explicitly set the project ID from environment variable
          projectId: firebaseProjectId,
          // Use Application Default Credentials for authentication
          // In production: uses the attached service account
        });
        
        this.logger.warn(`Firebase Admin SDK initialized successfully with project ID: ${firebaseProjectId}`);
      }
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK:', error);
      throw error;
    }
  }

  getAuth() {
    try {
      return admin.auth();
    } catch (error) {
      this.logger.error('Failed to get Firebase Auth instance:', error);
      throw error;
    }
  } 
}