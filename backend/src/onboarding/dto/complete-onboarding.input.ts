import { InputType, Field, ID } from '@nestjs/graphql';
import { Type, Transform } from 'class-transformer';
import {
  IsBoolean,
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDate,
  ValidateNested,
  IsArray,
} from 'class-validator';

@InputType()
class ConsentInput {
  @Field(() => Boolean)
  @IsBoolean()
  dataPrivacy: boolean;

  @Field(() => Boolean)
  @IsBoolean()
  dataSharing: boolean;

  @Field(() => Boolean)
  @IsBoolean()
  marketing: boolean;
}

@InputType()
class PersonalInfoInput {
  @Field()
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @Field()
  @Transform(({ value }) => new Date(value))
  @IsDate()
  birthdate: Date;

  @Field()
  @IsString()
  @IsNotEmpty()
  gender: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  countryCode: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  countryName: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  language: string;
}

@InputType()
class ProfilePictureInput {
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  imageUri?: string;
}

@InputType()
class DiseaseSelectionInput {
  @Field()
  @IsString()
  id: string;

  @Field()
  @IsString()
  icdCode: string;
}

@InputType()
class DiseaseDataInput {
  @Field(() => [DiseaseSelectionInput])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DiseaseSelectionInput)
  selectedDiseases: DiseaseSelectionInput[];
}

@InputType()
class UserTypeDataInput {
  @Field(() => String)
  @IsString()
  diseaseUserTypes: string;
}

@InputType()
class MedicationItemInput {
  @Field()
  @IsString()
  id: string;

  @Field()
  @IsString()
  label: string;
}

@InputType()
class MedicationEntryInput {
  @Field(() => MedicationItemInput)
  @ValidateNested()
  @Type(() => MedicationItemInput)
  medication: MedicationItemInput;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  dosage?: string;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  frequency?: string;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  notes?: string;

  @Field({ nullable: true })
  @Transform(({ value }) => value ? new Date(value) : null)
  @IsOptional()
  @IsDate()
  startDate?: Date;

  @Field()
  @IsBoolean()
  isCurrent: boolean;
}

@InputType()
class MedicationDataInput {
  @Field(() => String)
  @IsString()
  diseaseRelatedMedications: string;

  @Field(() => [MedicationEntryInput])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MedicationEntryInput)
  unrelatedMedications: MedicationEntryInput[];
}

@InputType()
class MedicalDeviceItemInput {
  @Field()
  @IsString()
  id: string;

  @Field()
  @IsString()
  label: string;
}

@InputType()
class MedicalDeviceEntryInput {
  @Field(() => MedicalDeviceItemInput)
  @ValidateNested()
  @Type(() => MedicalDeviceItemInput)
  device: MedicalDeviceItemInput;
}

@InputType()
class MedicalDeviceDataInput {
  @Field(() => String)
  @IsString()
  diseaseRelatedMedicalDevices: string;
}

@InputType()
export class CompleteOnboardingInput {
  @Field(() => ConsentInput)
  @ValidateNested()
  @Type(() => ConsentInput)
  consent: ConsentInput;

  @Field(() => PersonalInfoInput)
  @ValidateNested()
  @Type(() => PersonalInfoInput)
  personalInfo: PersonalInfoInput;

  @Field(() => ProfilePictureInput)
  @ValidateNested()
  @Type(() => ProfilePictureInput)
  profilePicture: ProfilePictureInput;

  @Field(() => DiseaseDataInput)
  @ValidateNested()
  @Type(() => DiseaseDataInput)
  diseases: DiseaseDataInput;

  @Field(() => UserTypeDataInput)
  @ValidateNested()
  @Type(() => UserTypeDataInput)
  userTypes: UserTypeDataInput;

  @Field(() => MedicationDataInput)
  @ValidateNested()
  @Type(() => MedicationDataInput)
  medications: MedicationDataInput;

  @Field(() => MedicalDeviceDataInput, { nullable: true })
  @ValidateNested()
  @Type(() => MedicalDeviceDataInput)
  @IsOptional()
  medicalDevices?: MedicalDeviceDataInput;
}