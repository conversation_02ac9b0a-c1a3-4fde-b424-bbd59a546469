import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from '../users/user.entity';
import { StorageService } from './storage.service';
import { UsersService } from '../users/users.service';
import { UploadUrlResponse } from './dto/upload.dto';
import { extension } from 'mime-types';
import { Logger } from '@nestjs/common';

@Resolver()
export class StorageResolver {
  private readonly logger = new Logger(StorageResolver.name);

  constructor(
    private readonly storageService: StorageService,
    private readonly usersService: UsersService,
  ) {}

  @Mutation(() => UploadUrlResponse)
  async createProfileImageUploadUrl(
    @CurrentUser() user: User,
    @Args('contentType') contentType: string,
  ): Promise<UploadUrlResponse> {
    try {
      const timestamp = Date.now();
      const ext = extension(contentType);
      const objectPath = `profile-images/${user.id}/${timestamp}.${ext}`;
      
      const signedUrl = await this.storageService.generateUploadUrl(
        objectPath,
        contentType,
      );
      
      const publicUrl = this.storageService.getPublicUrl(objectPath);

      return {
        signedUrl,
        publicUrl,
      };
    } catch (error) {
      this.logger.error(`Failed to create profile image upload URL: ${error.message}`);
      throw error;
    }
  }

  @Mutation(() => UploadUrlResponse)
  async createThreadImageUploadUrl(
    @CurrentUser() user: User,
    @Args('contentType') contentType: string,
  ): Promise<UploadUrlResponse> {
    try {
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substring(2, 8);
      const objectPath = `thread-images/${user.id}/${timestamp}-${randomSuffix}.jpeg`;
      
      const signedUrl = await this.storageService.generateUploadUrl(
        objectPath,
        contentType,
      );
      
      const publicUrl = this.storageService.getPublicUrl(objectPath);

      return {
        signedUrl,
        publicUrl,
      };
    } catch (error) {
      this.logger.error(`Failed to create thread image upload URL: ${error.message}`);
      throw error;
    }
  }

  @Mutation(() => User)
  async updateUserProfileImage(
    @CurrentUser() user: User,
    @Args('profileImageUrl') profileImageUrl: string,
  ): Promise<User> {
    try {
      return await this.usersService.updateProfileImageUrls(
        user.id,
        profileImageUrl,
      );
    } catch (error) {
      this.logger.error(`Failed to update user profile image: ${error.message}`);
      throw error;
    }
  }
}
