import { ObjectType, Field, ID, Int, InputType } from '@nestjs/graphql';
import { ObjectId } from 'mongodb';
import { IsString, IsNotEmpty, IsOptional, IsArray, ValidateNested, IsDefined } from 'class-validator';
import { Type } from 'class-transformer';

@ObjectType()
export class ReactionCounts {
  @Field(() => Int)
  love: number;

  @Field(() => Int)
  withYou: number;

  @Field(() => Int)
  funny: number;

  @Field(() => Int)
  insightful: number;

  @Field(() => Int)
  poop: number;
}

@ObjectType()
export class Author {
  @Field()
  authorId: string;

  @Field()
  displayName: string;

  @Field({ nullable: true })
  condition?: string;

  @Field()
  userType: string;

  @Field({ nullable: true })
  photoURL?: string;
}

@InputType()
export class AuthorInput {
  @Field()
  @IsString()
  @IsNotEmpty()
  authorId: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  displayName: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  condition?: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  userType: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  photoURL?: string;
}

@ObjectType('Thread')
export class Thread {
  @Field(() => ID)
  _id: ObjectId;

  @Field()
  communityId: string;

  @Field(() => Author)
  author: Author;

  @Field()
  title: string;

  @Field()
  content: string;

  @Field(() => [String])
  labels: string[];

  @Field()
  status: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field(() => Int)
  commentCount: number;

  @Field(() => ReactionCounts)
  reactionCounts: ReactionCounts;

  @Field(() => String, { nullable: true })
  myReaction?: string | null;

  @Field(() => [String])
  imageUrls: string[];

  @Field({ nullable: true })
  deletedAt?: Date;

  @Field(() => Boolean)
  isAuthor?: boolean;
}

@InputType()
export class CreateThreadInput {
  @Field()
  @IsString()
  @IsNotEmpty()
  title: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  content: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  communityId: string;

  @Field(() => AuthorInput)
  @IsDefined()
  @ValidateNested()
  @Type(() => AuthorInput)
  author: AuthorInput;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  labels?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imageUrls?: string[];
} 