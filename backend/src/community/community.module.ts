import { Module } from '@nestjs/common';
import {
  ThreadResolver,
  CommentResolver,
  FeedbackResolver,
} from './community.resolver';
import { ThreadsModule } from './threads/threads.module';
import { CommentsModule } from './comments/comments.module';
import { ReactionsModule } from './reactions/reactions.module';
import { FeedbackModule } from './feedback/feedback.module';
import { CommunityService } from './community.service';
import { PrismaModule } from '../prisma/prisma.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    ThreadsModule,
    CommentsModule,
    ReactionsModule,
    FeedbackModule,
    PrismaModule,
    UsersModule,
  ],
  providers: [
    ThreadResolver,
    CommentResolver,
    FeedbackResolver,
    CommunityService,
  ],
  exports: [CommunityService],
})
export class CommunityModule {}
