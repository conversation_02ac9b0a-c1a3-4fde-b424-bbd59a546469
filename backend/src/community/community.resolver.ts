import {
  Resolver,
  Query,
  Args,
  Int,
  ID,
  Mutation,
  Parent,
  ResolveField,
} from '@nestjs/graphql';
import { ReactionsService } from './reactions/reactions.service';
import { ThreadsService } from './threads/threads.service';
import { CommentsService } from './comments/comments.service';
import { Thread, CreateThreadInput } from './threads/dto/threads.models';
import { Comment, CreateCommentInput } from './comments/dto/comments.models';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from 'src/users/user.entity';
import { ForbiddenException } from '@nestjs/common';
import {
  Feedback,
  CreateFeedbackInput,
} from './feedback/dto/createFeedback.input';
import { FeedbackService } from './feedback/feedback.service';
import { Public } from '../auth/public.decorator';
import { UsersService } from '../users/users.service';

@Resolver(() => Thread)
export class ThreadResolver {
  constructor(
    private readonly reactionsService: ReactionsService,
    private readonly threadsService: ThreadsService,
    private readonly commentsService: CommentsService,
    private readonly usersService: UsersService,
  ) {}

  @Query(() => [Thread], { name: 'threads' })
  async getThreads(
    @CurrentUser() user: User,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 })
    limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 })
    offset: number,
    @Args('communityIds', { type: () => [String], nullable: true })
    communityIds?: string[],
    @Args('cursor', { type: () => String, nullable: true })
    cursor?: string,
    @Args('since', { type: () => String, nullable: true })
    since?: string,
  ) {
    // 1. Fetch the main documents (threads)
    const threads = await this.threadsService.getThreads({
      limit,
      offset,
      communityIds,
      cursor,
      since,
    });
    if (!user || threads.length === 0) {
      return threads;
    }

    // 2. Collect IDs for batch fetching
    const threadIds = threads.map((t) => t._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions =
      await this.reactionsService.findUserReactionsForThreads(
        threadIds,
        user.id,
      );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    threads.forEach((thread) => {
      thread.myReaction = reactionMap.get(thread._id.toHexString()) || null;
    });

    return threads;
  }

  @Query(() => Thread, { name: 'thread' })
  async getThreadById(
    @CurrentUser() user: User,
    @Args('id', { type: () => ID }) id: string,
  ) {
    const thread = await this.threadsService.getThreadById(id);
    if (!user || !thread) {
      return thread;
    }
    const reaction = await this.reactionsService.findUserReactionForThread(
      thread._id,
      user.id,
    );
    thread.myReaction = reaction ? reaction.reactionType : null;
    return thread;
  }

  @Mutation(() => Thread, { name: 'createThread' })
  async createThread(
    @Args('input') createThreadInput: CreateThreadInput,
  ): Promise<Thread> {
    return this.threadsService.createThread(createThreadInput);
  }

  @ResolveField('myReaction', () => String, { nullable: true })
  myReaction(@Parent() thread: Thread): string | null {
    // This will now be resolved by the parent queries (getThreads, getThreadById)
    // and attached directly. If it's not there, it's null.
    return thread.myReaction || null;
  }

  @ResolveField('isAuthor', () => Boolean)
  async isAuthor(@Parent() thread: Thread, @CurrentUser() user: User): Promise<boolean> {
    if (!user) {
      return false;
    }
    
    // Get the pseudonym for the current user
    const userPseudonym = await this.usersService.getOrCreatePseudonym(user.id);
    
    // Compare pseudonymIds
    return thread.author.authorId === userPseudonym.pseudonymId;
  }

  @Query(() => [Comment], { name: 'comments' })
  async getComments(
    @CurrentUser() user: User,
    @Args('threadId', { type: () => ID }) threadId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 20 }) limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset: number,
  ) {
    // 1. Fetch the main documents (comments)
    const comments = await this.commentsService.getCommentsForThread(threadId, { limit, offset });
    if (!user || comments.length === 0) {
      return comments;
    }

    // 2. Collect IDs for batch fetching
    const commentIds = comments.map((c) => c._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions = await this.reactionsService.findUserReactionsForComments(
      commentIds,
      user.id,
    );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    comments.forEach((comment) => {
      comment.myReaction = reactionMap.get(comment._id.toHexString()) || null;
    });

    return comments;
  }

  @Query(() => [Comment], { name: 'replies' })
  async getReplies(
    @CurrentUser() user: User,
    @Args('parentCommentId', { type: () => ID }) parentCommentId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset: number,
  ) {
    // 1. Fetch the main documents (replies)
    const replies = await this.commentsService.getRepliesForComment(parentCommentId, { limit, offset });
    if (!user || replies.length === 0) {
      return replies;
    }

    // 2. Collect IDs for batch fetching
    const replyIds = replies.map((r) => r._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions = await this.reactionsService.findUserReactionsForComments(
      replyIds,
      user.id,
    );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    replies.forEach((reply) => {
      reply.myReaction = reactionMap.get(reply._id.toHexString()) || null;
    });

    return replies;
  }

  @Mutation(() => Thread, { name: 'addReaction' })
  async addReaction(
    @CurrentUser() user: User,
    @Args('threadId', { type: () => ID }) threadId: string,
    @Args('reaction', { type: () => String }) reaction: string,
  ) {
    const thread = await this.reactionsService.addReactionToThread(
      threadId,
      user.id,
      reaction,
    );

    // After mutation, we need to manually add the user's reaction
    // to the returned object so the frontend gets the updated state.
    const userReaction = await this.reactionsService.findUserReactionForThread(
      thread._id,
      user.id,
    );
    thread.myReaction = userReaction ? userReaction.reactionType : null;
    return thread;
  }

  @Mutation(() => Thread, { name: 'deleteThread' })
  async deleteThread(
    @CurrentUser() user: User,
    @Args('threadId', { type: () => ID }) threadId: string,
  ): Promise<Thread> {
    return this.threadsService.softDeleteThread(threadId, user.id);
  }
}

@Resolver(() => Comment)
export class CommentResolver {
  constructor(
    private readonly reactionsService: ReactionsService,
    private readonly commentsService: CommentsService,
    private readonly threadsService: ThreadsService,
  ) {}

  @ResolveField('myReaction', () => String, { nullable: true })
  myReaction(@Parent() comment: Comment): string | null {
    // This will now be resolved by the parent queries (getComments, getReplies)
    // and attached directly. If it's not there, it's null.
    return comment.myReaction || null;
  }

  @Mutation(() => Comment, { name: 'createComment' })
  async createComment(
    @CurrentUser() user: User,
    @Args('input') input: CreateCommentInput,
  ): Promise<Comment> {
    if (user.id !== input.author.authorId) {
      throw new ForbiddenException("You can only create comments for yourself.");
    }

    const comment = await this.commentsService.createComment(input);
    
    // Don't wait for this to complete
    this.threadsService.incrementCommentCount(input.threadId);

    // Eagerly load the communityId from the parent thread for refetching
    const thread = await this.threadsService.getThreadById(input.threadId);
    (comment as any).communityId = thread.communityId;

    return comment;
  }

  @Mutation(() => Comment, { name: 'addCommentReaction' })
  async addCommentReaction(
    @CurrentUser() user: User,
    @Args('commentId', { type: () => ID }) commentId: string,
    @Args('reaction', { type: () => String }) reaction: string,
  ) {
    const comment = await this.reactionsService.addReactionToComment(
      commentId,
      user.id,
      reaction,
    );

    // After mutation, we need to manually add the user's reaction
    // to the returned object so the frontend gets the updated state.
    const userReaction = await this.reactionsService.findUserReactionForComment(
      comment._id,
      user.id,
    );
    comment.myReaction = userReaction ? userReaction.reactionType : null;
    return comment;
  }
}

@Resolver(() => Feedback)
export class FeedbackResolver {
  constructor(private readonly feedbackService: FeedbackService) {}

  @Public()
  @Mutation(() => Boolean, { name: 'createFeedback' })
  async createFeedback(
    @Args('createFeedbackInput') createFeedbackInput: CreateFeedbackInput,
  ): Promise<boolean> {
    return this.feedbackService.createFeedback(createFeedbackInput);
  }
}
