import { Module } from '@nestjs/common';
import { ReactionsResolver } from './reactions.resolver';
import { ReactionsService } from './reactions.service';
import { MongoModule } from '../../mongo/mongo.module';
import { UsersModule } from '../../users/users.module';
import { NotificationsModule } from 'src/notifications/notifications.module';

@Module({
  imports: [MongoModule, UsersModule, NotificationsModule],
  providers: [ReactionsResolver, ReactionsService],
  exports: [ReactionsService],
})
export class ReactionsModule {}
