import { ObjectType, Field, ID, InputType } from '@nestjs/graphql';
import { ObjectId } from 'mongodb';
import { IsString, IsNotEmpty } from 'class-validator';

@ObjectType('Reaction')
export class Reaction {
  @Field(() => ID)
  _id: ObjectId;

  @Field(() => ID)
  documentId: ObjectId; // ID of the Thread or Comment

  @Field()
  documentType: 'Thread' | 'Comment';

  @Field()
  reactorId: string; // The ID of the user who reacted

  @Field()
  reactionType: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@InputType()
export class AddReactionInput {
  @Field()
  @IsString()
  @IsNotEmpty()
  documentId: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  documentType: 'Thread' | 'Comment';

  @Field()
  @IsString()
  @IsNotEmpty()
  reactionType: string;
} 