import { ObjectType, Field, ID, Int, InputType } from '@nestjs/graphql';
import { ObjectId } from 'mongodb';
import { ReactionCounts, Author, AuthorInput } from '../../threads/dto/threads.models';
import { IsString, IsNotEmpty, IsOptional, ValidateNested, IsDefined } from 'class-validator';
import { Type } from 'class-transformer';

@ObjectType('Comment')
export class Comment {
    @Field(() => ID)
    _id: ObjectId;

    @Field(() => ID)
    threadId: ObjectId;

    @Field(() => ID, { nullable: true })
    communityId?: string;

    @Field(() => Author)
    author: Author;

    @Field(() => ID, { nullable: true })
    parentCommentId?: ObjectId;

    @Field()
    content: string;

    @Field()
    status: string;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;

    @Field(() => Int)
    replyCount: number;

    @Field(() => ReactionCounts)
    reactionCounts: ReactionCounts;

    @Field(() => String, { nullable: true })
    myReaction?: string | null;
} 

@InputType()
export class CreateCommentInput {
    @Field(() => ID)
    @IsString()
    @IsNotEmpty()
    threadId: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    content: string;

    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    parentCommentId?: string;

    @Field(() => AuthorInput)
    @IsDefined()
    @ValidateNested()
    @Type(() => AuthorInput)
    author: AuthorInput;
} 