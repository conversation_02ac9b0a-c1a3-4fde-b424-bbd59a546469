import { Module, forwardRef } from '@nestjs/common';
import { CommentsService } from './comments.service';
import { MongoModule } from '../../mongo/mongo.module';
import { NotificationsModule } from '../../notifications/notifications.module';
import { UsersModule } from '../../users/users.module';

@Module({
  imports: [MongoModule, forwardRef(() => NotificationsModule), UsersModule],
  providers: [CommentsService],
  exports: [CommentsService],
})
export class CommentsModule {}
