import { Injectable, Logger } from '@nestjs/common';
import { CreateFeedbackInput, Feedback } from './dto/createFeedback.input';
import { google } from 'googleapis';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FeedbackService {
  private readonly logger = new Logger(FeedbackService.name);
  private readonly spreadsheetId = process.env.SPREADSHEET_ID;
  private readonly sheetName = 'feedback'; // As specified: the tab name is 'feedback'

  async createFeedback(createFeedbackInput: CreateFeedbackInput): Promise<boolean> {
    const { categories, text, name } = createFeedbackInput;

    const newFeedback: Feedback = {
      id: uuidv4(),
      categories,
      text,
      name: name || 'Anonymous',
      createdAt: new Date(),
    };
    
    try {
      await this.appendToSheet(newFeedback);
      return true;
    } catch (error) {
      this.logger.error('Failed to append data to Google Sheet', error.stack);
      return false;
    }
  }

  private async appendToSheet(feedback: Feedback) {
    if (!this.spreadsheetId) {
      this.logger.error('SPREADSHEET_ID is not configured in environment variables.');
      throw new Error('Spreadsheet ID is not configured.');
    }

    const auth = new google.auth.GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    const sheets = google.sheets({ version: 'v4', auth });

    const date = feedback.createdAt;
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const formattedDate = `${day}-${month}-${year} ${hours}:${minutes}`;

    // Column order: Name, tags, feedback, createdAt
    const row = [
      feedback.name,
      feedback.categories.join(', '), // Convert array to comma-separated string for 'tags'
      feedback.text,
      formattedDate,
    ];

    await sheets.spreadsheets.values.append({
      spreadsheetId: this.spreadsheetId,
      range: this.sheetName, // Appends to the first empty row of the sheet
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: [row],
      },
    });

    this.logger.log(`Successfully appended feedback from "${feedback.name}" to sheet.`);
  }
}
