import { InputType, Field, ObjectType, ID } from '@nestjs/graphql';
import { IsArray, IsOptional, IsString, Length, MinLength } from 'class-validator';

@InputType()
export class CreateFeedbackInput {
  @Field(() => [String])
  @IsArray()
  categories: string[];

  @Field()
  @IsString()
  @MinLength(1)
  text: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  name?: string;
}

@ObjectType()
export class Feedback {
  @Field(() => ID)
  id: string;

  @Field(() => [String])
  categories: string[];

  @Field()
  text: string;

  @Field()
  createdAt: Date;

  @Field(() => String, { nullable: true })
  name?: string;
}
