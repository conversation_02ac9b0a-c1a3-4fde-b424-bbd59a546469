import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { NotificationsService } from './notifications.service';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from '../users/user.entity';
import { SavePushTokenInput } from './dto/save-push-token.input';

@Resolver()
export class NotificationsResolver {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Mutation(() => Boolean)
  async savePushToken(
    @CurrentUser() user: User,
    @Args('input') input: SavePushTokenInput,
  ): Promise<boolean> {
    return this.notificationsService.savePushToken(user.id, input);
  }

  @Mutation(() => Boolean)
  async removePushToken(
    @Args('token') token: string,
  ): Promise<boolean> {
    return this.notificationsService.removePushToken(token);
  }
}
