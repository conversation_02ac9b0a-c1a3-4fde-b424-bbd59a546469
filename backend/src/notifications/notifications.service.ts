import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SavePushTokenInput } from './dto/save-push-token.input';
import { HttpService } from '@nestjs/axios';
import { CommunityService } from '../community/community.service';
import { AxiosError } from 'axios';

// Types for Expo Push API response
interface ExpoPushTicket {
  status: 'ok' | 'error';
  id?: string;
  message?: string;
  details?: {
    error?: string;
  };
}

interface ExpoPushResponse {
  data: ExpoPushTicket[];
  errors?: Array<{
    code: string;
    message: string;
  }>;
}

// Type for notification payload
interface ExpoPushNotificationPayload {
  to: string | string[];
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: string;
  badge?: number;
  channelId?: string;
  priority?: 'default' | 'normal' | 'high';
  ttl?: number;
  expiration?: number;
}

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private prisma: PrismaService,
    private readonly httpService: HttpService,
    private readonly communityService: CommunityService,
  ) {}

  private async getUserIdFromPseudonymId(pseudonymId: string): Promise<string | null> {
    const pseudonym = await this.prisma.client.user_pseudonyms.findUnique({
      where: { pseudonymId },
      select: { userId: true }
    });
    return pseudonym?.userId || null;
  }

  /**
   * Processes Expo Push API response tickets and deletes tokens that are marked as DeviceNotRegistered
   * This is Strategy 1: Reactive Deletion (On Failure)
   */
  private async processExpoPushTickets(tokens: string[], tickets: ExpoPushTicket[]): Promise<void> {
    const tokensToDelete: string[] = [];

    // Iterate through tickets and identify tokens that need to be deleted
    for (let i = 0; i < tickets.length; i++) {
      const ticket = tickets[i];
      const token = tokens[i];

      if (ticket.status === 'error' && ticket.details?.error === 'DeviceNotRegistered') {
        tokensToDelete.push(token);
        this.logger.warn(`Token ${token.substring(0, 20)}... marked as DeviceNotRegistered, scheduling for deletion`);
      }
    }

    // Delete all invalid tokens in a single database transaction
    if (tokensToDelete.length > 0) {
      try {
        await this.prisma.client.push_tokens.deleteMany({
          where: {
            token: {
              in: tokensToDelete,
            },
          },
        });

        this.logger.log(`Successfully deleted ${tokensToDelete.length} invalid push tokens`);
      } catch (error) {
        this.logger.error('Failed to delete invalid push tokens:', error);
      }
    }
  }

  /**
   * Sends push notifications in chunks and handles reactive deletion of invalid tokens
   */
  private async sendExpoPushNotifications(notificationPayload: ExpoPushNotificationPayload): Promise<void> {
    // Extract tokens array (handle both single token and array cases)
    const allTokens: string[] = Array.isArray(notificationPayload.to) 
      ? notificationPayload.to 
      : [notificationPayload.to];

    if (allTokens.length === 0) {
      this.logger.log('No tokens to send notifications to');
      return;
    }

    // Chunk tokens into groups of 100 as recommended by Expo
    const chunks: string[][] = [];
    const chunkSize = 100;
    for (let i = 0; i < allTokens.length; i += chunkSize) {
      chunks.push(allTokens.slice(i, i + chunkSize));
    }

    this.logger.log(`Sending notifications to ${allTokens.length} token(s) in ${chunks.length} chunk(s)...`);

    // Send all chunks in parallel and wait for them to complete
    await Promise.all(
      chunks.map(async (chunk: string[], chunkIndex: number) => {
        const payload: ExpoPushNotificationPayload = { ...notificationPayload, to: chunk };
        try {
          const response = await this.httpService.axiosRef.post<ExpoPushResponse>(
            'https://exp.host/--/api/v2/push/send',
            payload,
            {
              headers: {
                'host': 'exp.host',
                'accept': 'application/json',
                'accept-encoding': 'gzip, deflate',
                'content-type': 'application/json',
              },
            }
          );

          // Process response tickets for reactive deletion
          if (response.data?.data && Array.isArray(response.data.data)) {
            await this.processExpoPushTickets(chunk, response.data.data);
          }

          this.logger.log(`Chunk ${chunkIndex + 1}/${chunks.length} sent successfully to ${chunk.length} token(s)`);
        } catch (error) {
          if (error instanceof AxiosError) {
            this.logger.error(`Error sending notification chunk ${chunkIndex + 1}/${chunks.length}:`, error.response?.data);
          } else {
            this.logger.error(`An unexpected error occurred sending chunk ${chunkIndex + 1}/${chunks.length}:`, error);
          }
          // Don't throw here - we want other chunks to continue processing
          // Instead, log the error and continue with remaining chunks
        }
      }),
    );

    this.logger.log(`Finished sending notifications to all ${allTokens.length} token(s)`);
  }

  async sendNewThreadNotification(
    threadId: string,
    communityId: string,
    authorId: string,
    threadTitle: string,
    threadContent: string,
  ) {
    this.logger.log(`New thread notification process started for community ${communityId}`);

    try {
      const community = await this.communityService.getCommunityById(communityId);
      if (!community) {
        this.logger.warn(`Community with ID ${communityId} not found. Aborting notification.`);
        return;
      }

      const pushTokens: { token: string }[] = await this.prisma.client.$queryRaw`
        SELECT t.token
        FROM "identity".push_tokens AS t
        INNER JOIN "user_vault".user_pseudonyms AS p ON t."userId" = p."userId"
        INNER JOIN "health"."CommunityMembership" AS cm ON p."pseudonymId" = cm."user_healthID"
        WHERE cm."communityId" = ${communityId} AND t."userId" != ${authorId}
      `;

      const tokens = pushTokens.map(t => t.token);
      if (tokens.length === 0) {
        this.logger.log('No push tokens found for users in the community.');
        return;
      }

      this.logger.debug('Push tokens for community members: ', pushTokens);

      const notificationPayload = {
        to: tokens,
        title: 'New post in your community!',
        body: `${threadTitle}:\n${threadContent}`,
        data: { threadId },
      };

      await this.sendExpoPushNotifications(notificationPayload);
      
      this.logger.log(`Successfully sent notifications for new thread ${threadId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error('Error sending push notifications:', error.response?.data);
      } else {
        this.logger.error('An unexpected error occurred during new thread notification:', error);
      }
    }
  }

  async sendNewCommentNotification(
    threadId: string,
    communityId: string,
    commentAuthorPseudonymId: string,
    threadAuthorPseudonymId: string,
    threadTitle: string,
    commentContent: string,
  ) {
    this.logger.log(`New comment notification process started for thread ${threadId}`);

    try {
      const community = await this.communityService.getCommunityById(communityId);
      if (!community) {
        this.logger.warn(`Community with ID ${communityId} not found. Aborting notification.`);
        return;
      }

      // First, get the actual userIds from the pseudonymIds
      const threadAuthorUserId = await this.getUserIdFromPseudonymId(threadAuthorPseudonymId);
      const commentAuthorUserId = await this.getUserIdFromPseudonymId(commentAuthorPseudonymId);

      if (!threadAuthorUserId || !commentAuthorUserId) {
        this.logger.warn('Could not find user IDs for pseudonym IDs. Aborting notification.');
        return;
      }

      // Get push tokens for the thread author only (not the comment author)
      const pushTokens: { token: string }[] = await this.prisma.client.$queryRaw`
        SELECT t.token
        FROM "identity".push_tokens AS t
        WHERE t."userId" = ${threadAuthorUserId} AND t."userId" != ${commentAuthorUserId}
      `;

      // Get name of the comment author using raw SQL since no Prisma relation exists
      const commentAuthor: { displayName: string }[] = await this.prisma.client.$queryRaw`
        SELECT u."displayName"
        FROM "user_vault".user_pseudonyms up
        JOIN "identity".users u ON u.id = up."userId"
        WHERE up."pseudonymId" = ${commentAuthorPseudonymId}
      `;

      const tokens = pushTokens.map(t => t.token);
      if (tokens.length === 0) {
        this.logger.log('No push tokens found for the thread author or thread author is the comment author.');
        return;
      }

      const authorName = commentAuthor[0]?.displayName || 'Someone';

      this.logger.debug('Push tokens for thread author: ', pushTokens);

      const notificationPayload = {
        to: tokens,
        title: 'New comment on your post',
        body: `${authorName} commented on "${threadTitle}": ${commentContent}`,
        data: { threadId },
      };

      await this.sendExpoPushNotifications(notificationPayload);

      this.logger.log(`Successfully sent comment notification for thread ${threadId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error('Error sending push notifications:', error.response?.data);
      } else {
        this.logger.error('An unexpected error occurred during new comment notification:', error);
      }
    }
  }

  async sendNewReplyNotification(
    threadId: string,
    communityId: string,
    replyAuthorPseudonymId: string,
    parentCommentAuthorPseudonymId: string,
    threadTitle: string,
    replyContent: string,
  ) {
    this.logger.log(`New reply notification process started for thread ${threadId}`);

    try {
      const community = await this.communityService.getCommunityById(communityId);
      if (!community) {
        this.logger.warn(`Community with ID ${communityId} not found. Aborting notification.`);
        return;
      }

      // First, get the actual userIds from the pseudonymIds
      const parentCommentAuthorUserId = await this.getUserIdFromPseudonymId(parentCommentAuthorPseudonymId);
      const replyAuthorUserId = await this.getUserIdFromPseudonymId(replyAuthorPseudonymId);

      if (!parentCommentAuthorUserId || !replyAuthorUserId) {
        this.logger.warn('Could not find user IDs for pseudonym IDs. Aborting notification.');
        return;
      }

      // Get push tokens for the parent comment author only (not the reply author)
      const pushTokens: { token: string }[] = await this.prisma.client.$queryRaw`
        SELECT t.token
        FROM "identity".push_tokens AS t
        WHERE t."userId" = ${parentCommentAuthorUserId} AND t."userId" != ${replyAuthorUserId}
      `;

      // Get name of the reply author using raw SQL since no Prisma relation exists
      const replyAuthor: { firstName: string; lastName: string }[] = await this.prisma.client.$queryRaw`
        SELECT u."firstName", u."lastName"
        FROM "user_vault".user_pseudonyms up
        JOIN "identity".users u ON u.id = up."userId"
        WHERE up."pseudonymId" = ${replyAuthorPseudonymId}
      `;

      this.logger.debug('Reply author pseudonym: ', replyAuthorPseudonymId);
      this.logger.debug('Reply author: ', replyAuthor);

      const tokens = pushTokens.map(t => t.token);
      if (tokens.length === 0) {
        this.logger.log('No push tokens found for the parent comment author or parent comment author is the reply author.');
        return;
      }

      const authorName = replyAuthor[0]?.firstName + ' ' + replyAuthor[0]?.lastName || 'Someone';

      this.logger.debug('Push tokens for parent comment author: ', pushTokens);

      const notificationPayload = {
        to: tokens,
        title: 'New reply to your comment',
        body: `${authorName} replied to your comment on "${threadTitle}": ${replyContent}`,
        data: { threadId },
      };

      await this.sendExpoPushNotifications(notificationPayload);

      this.logger.log(`Successfully sent reply notification for thread ${threadId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error('Error sending push notifications:', error.response?.data);
      } else {
        this.logger.error('An unexpected error occurred during new reply notification:', error);
      }
    }
  }

  async savePushToken(userId: string, input: SavePushTokenInput): Promise<boolean> {
    try {
      this.logger.log(`Saving push token for user ${userId}`);

      // Use upsert to handle both new tokens and updates
      // This ensures we don't create duplicates and handle token updates properly
      await this.prisma.client.push_tokens.upsert({
        where: {
          token: input.token,
        },
        update: {
          userId: userId,
          updatedAt: new Date(),
        },
        create: {
          token: input.token,
          userId: userId,
        },
      });

      this.logger.log(`Successfully saved push token for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to save push token for user ${userId}:`, error);
      throw error;
    }
  }

  async removePushToken(token: string): Promise<boolean> {
    try {
      this.logger.log(`Removing push token: ${token.substring(0, 10)}...`);

      await this.prisma.client.push_tokens.delete({
        where: {
          token: token,
        },
      });

      this.logger.log(`Successfully removed push token`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to remove push token:`, error);
      throw error;
    }
  }

  async sendThreadReactionNotification(
    threadId: string,
    reactorUserId: string,
    threadAuthorPseudonymId: string,
    threadTitle: string,
    reactionType: string,
  ) {
    this.logger.log(`Thread reaction notification process started for thread ${threadId}`);

    try {
      // Convert thread author pseudonymId to userId
      const threadAuthorUserId = await this.getUserIdFromPseudonymId(threadAuthorPseudonymId);

      if (!threadAuthorUserId) {
        this.logger.warn('Could not find thread author user ID. Aborting notification.');
        return;
      }

      // Don't send notification if the user is reacting to their own thread
      if (threadAuthorUserId === reactorUserId) {
        this.logger.log('Thread author and reactor are the same user. Skipping notification.');
        return;
      }

      // Get both push tokens and reactor display name in parallel
      const [pushTokens, reactor] = await Promise.all([
        this.prisma.client.$queryRaw<{ token: string }[]>`
          SELECT t.token
          FROM "identity".push_tokens AS t
          WHERE t."userId" = ${threadAuthorUserId}
        `,
        this.prisma.client.$queryRaw<{ displayName: string }[]>`
          SELECT u."displayName"
          FROM "identity".users u
          WHERE u.id = ${reactorUserId}
        `
      ]);

      const tokens = pushTokens.map(t => t.token);
      if (tokens.length === 0) {
        this.logger.log('No push tokens found for the thread author.');
        return;
      }

      const reactorName = reactor[0]?.displayName || 'Someone';

      this.logger.debug('Push tokens for thread author: ', pushTokens);

      const notificationPayload = {
        to: tokens,
        title: 'Someone reacted to your post',
        body: `${reactorName} reacted with ${reactionType} to "${threadTitle}"`,
        data: { threadId, reactionType },
      };

      await this.sendExpoPushNotifications(notificationPayload);

      this.logger.log(`Successfully sent thread reaction notification for thread ${threadId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error('Error sending thread reaction push notifications:', error.response?.data);
      } else {
        this.logger.error('An unexpected error occurred during thread reaction notification:', error);
      }
    }
  }

  async sendCommentReactionNotification(
    commentId: string,
    threadId: string,
    reactorUserId: string,
    commentAuthorPseudonymId: string,
    threadTitle: string,
    reactionType: string,
  ) {
    this.logger.log(`Comment reaction notification process started for comment ${commentId}`);

    try {
      // Convert comment author pseudonymId to userId
      const commentAuthorUserId = await this.getUserIdFromPseudonymId(commentAuthorPseudonymId);

      if (!commentAuthorUserId) {
        this.logger.warn('Could not find comment author user ID. Aborting notification.');
        return;
      }

      // Don't send notification if the user is reacting to their own comment
      if (commentAuthorUserId === reactorUserId) {
        this.logger.log('Comment author and reactor are the same user. Skipping notification.');
        return;
      }

      // Get both push tokens and reactor display name in parallel
      const [pushTokens, reactor] = await Promise.all([
        this.prisma.client.$queryRaw<{ token: string }[]>`
          SELECT t.token
          FROM "identity".push_tokens AS t
          WHERE t."userId" = ${commentAuthorUserId}
        `,
        this.prisma.client.$queryRaw<{ displayName: string }[]>`
          SELECT u."displayName"
          FROM "identity".users u
          WHERE u.id = ${reactorUserId}
        `
      ]);

      const tokens = pushTokens.map(t => t.token);
      if (tokens.length === 0) {
        this.logger.log('No push tokens found for the comment author.');
        return;
      }

      const reactorName = reactor[0]?.displayName || 'Someone';

      this.logger.debug('Push tokens for comment author: ', pushTokens);

      const notificationPayload = {
        to: tokens,
        title: 'Someone reacted to your comment',
        body: `${reactorName} reacted with ${reactionType} to your comment on "${threadTitle}"`,
        data: { threadId, commentId, reactionType },
      };

      await this.sendExpoPushNotifications(notificationPayload);

      this.logger.log(`Successfully sent comment reaction notification for comment ${commentId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error('Error sending comment reaction push notifications:', error.response?.data);
      } else {
        this.logger.error('An unexpected error occurred during comment reaction notification:', error);
      }
    }
  }

  async getUserPushTokens(userId: string): Promise<string[]> {
    try {
      const tokens = await this.prisma.client.push_tokens.findMany({
        where: {
          userId: userId,
        },
        select: {
          token: true,
        },
      });

      return tokens.map(t => t.token);
    } catch (error) {
      this.logger.error(`Failed to get push tokens for user ${userId}:`, error);
      throw error;
    }
  }
}
