import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { UsersService } from '../src/users/users.service';

async function run() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const prismaService = app.get(PrismaService);
  const usersService = app.get(UsersService);

  const emails = process.argv.slice(2);

  if (emails.length === 0) {
    console.error('Please provide at least one user email as an argument.');
    process.exit(1);
  }

  for (const email of emails) {
    try {
      console.log(`Attempting to clean up data for email: ${email}`);

      const user = await prismaService.client.users.findUnique({
        where: { email: email },
      });

      if (!user) {
        console.log(`No user found with email: ${email}. Nothing to clean up.`);
        continue;
      }

      const createdUserId = user.id;
      const pseudonym = await usersService.findUserPseudonym(createdUserId);
      const pseudonymId = pseudonym?.pseudonymId;

      if (!pseudonymId) {
        console.warn(`Could not find pseudonym for user ${createdUserId}. Cleanup may be incomplete.`);
      }

      console.log(`Cleaning up test data for user ${createdUserId} with pseudonym ${pseudonymId}`);

      await prismaService.client.$transaction(async (tx) => {
        if (pseudonymId) {
          await tx.communityMembership.deleteMany({ where: { user_healthID: pseudonymId } });
          await tx.qualityOfLifeLog.deleteMany({ where: { user_healthID: pseudonymId } });
          await tx.userMedication.deleteMany({ where: { user_healthID: pseudonymId } });
          await tx.userDiseaseProfile.deleteMany({ where: { user_healthID: pseudonymId } });
          await tx.user_pseudonyms.deleteMany({ where: { userId: createdUserId } });
        }
        await tx.user_consents.deleteMany({ where: { userId: createdUserId } });
        await tx.users.delete({ where: { id: createdUserId } });
      });

      console.log(`Cleanup completed successfully for email: ${email}`);
    } catch (error) {
      console.error(`An error occurred during cleanup for email ${email}:`, error);
    }
  }

  await app.close();
}

run().catch((error) => {
  console.error('An unexpected error occurred:', error);
  process.exit(1);
}); 