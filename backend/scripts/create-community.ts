
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // --- Configuration ---
  // Set the details for the new community here
  const name = "IBD Ambassadors EN";
  const description = "A community for sharing life experiences with Inflammatory Bowel Disease.";
  const icdCode = "DD7Z"; // ICD code for the disease to link (e.g., 'DD70' for Crohn's)
  const displayName = "IBD Ambassadors EN";
  // --- End of Configuration ---

  console.log(`Attempting to create community '${name}'...`);

  if (!name || !description || !icdCode || !displayName) {
    console.error(
      'Error: Please provide values for name, description, icdCode, and displayName in the script.',
    );
    process.exit(1);
  }

  const disease = await prisma.disease.findFirst({
    where: { icdCode: icdCode },
  });

  if (!disease) {
    console.error(`Error: Disease with ICD code '${icdCode}' not found.`);
    process.exit(1);
  }

  console.log(
    `Found disease '${disease.name}' (ID: ${disease.id}) for ICD code '${icdCode}'.`,
  );

  const existingCommunity = await prisma.community.findUnique({
    where: { name },
  });

  if (existingCommunity) {
    console.error(`Error: Community with name '${name}' already exists.`);
    process.exit(1);
  }

  const newCommunity = await prisma.community.create({
    data: {
      name,
      description,
      diseaseGroupId: disease.id,
      isActive: true,
      displayName,
    },
  });

  console.log(
    `Successfully created community '${newCommunity.name}' with ID: ${newCommunity.id}`,
  );
  console.log(`Linked to disease '${disease.name}'.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 