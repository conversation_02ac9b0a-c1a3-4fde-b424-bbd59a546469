import { ConfigService } from '@nestjs/config';
import { MongoService } from '../src/mongo/mongo.service';
import { Collection, Db, ObjectId } from 'mongodb';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// --- Configuration ---
// Add the string IDs of the threads you want to delete to this array.
const THREAD_IDS_TO_DELETE: string[] = [
    // Example: '668b3a8e93294c741b3a3c9b'
    '686fea86456ddf6558fd5911', 
];
const dbName = 'chronicare-forum-prod';
/**
 * This script removes threads and all their associated data (comments, replies, reactions)
 * from the database. It acts as a manual cascade delete.
 */
async function removeThreads() {
    if (THREAD_IDS_TO_DELETE.length === 0 || THREAD_IDS_TO_DELETE[0] === 'PASTE_THREAD_ID_HERE') {
        console.log("Please provide at least one thread ID in the THREAD_IDS_TO_DELETE array in this script.");
        console.log("The script will not run until you provide a valid ID.");
        return;
    }

    console.log("Starting script to remove threads and associated data...");

    const configService = new ConfigService();
    const mongoService = new MongoService(configService);
    let mongoClient;

    try {
        await mongoService.onModuleInit();
        mongoClient = mongoService.getClient();

        // Extract the database name from the connection string
        const mongoUrl = configService.get<string>('MONGO_DATABASE_URL');
        console.log("mongoUrl", mongoUrl);
        if (!mongoUrl) {
            throw new Error('MONGO_DATABASE_URL is not defined in your .env file.');
        }
        
        console.log("dbName", dbName);
        const db = mongoService.getDatabase(dbName); 
        
        if (!db) {
            throw new Error("Failed to get database instance. Check your MONGO_DATABASE_URL in the .env file.");
        }

        const Threads: Collection = db.collection('Threads');
        const Comments: Collection = db.collection('Comments');
        const Reactions: Collection = db.collection('Reactions');

        let totalThreadsDeleted = 0;
        let totalCommentsDeleted = 0;
        let totalReactionsDeleted = 0;

        for (const threadIdString of THREAD_IDS_TO_DELETE) {
            console.log(`\nProcessing thread with ID: ${threadIdString}`);
            
            if (!ObjectId.isValid(threadIdString)) {
                console.warn(` - SKIPPING: '${threadIdString}' is not a valid ObjectId.`);
                continue;
            }
            const threadId = new ObjectId(threadIdString);

            // 1. Find all comments and replies for the thread to get their IDs
            const commentsToDelete = await Comments.find({ threadId: threadId }).project({ _id: 1 }).toArray();
            const commentIdsToDelete = commentsToDelete.map(comment => comment._id);
            
            console.log(` - Found ${commentIdsToDelete.length} associated comment(s)/replies.`);

            // 2. Delete all reactions associated with the thread itself AND its comments/replies
            const allDocumentIdsToDelete = [threadId, ...commentIdsToDelete];
            const reactionDeletionResult = await Reactions.deleteMany({ documentId: { $in: allDocumentIdsToDelete } });
            
            if (reactionDeletionResult.deletedCount > 0) {
                console.log(` - Deleted ${reactionDeletionResult.deletedCount} associated reaction(s).`);
                totalReactionsDeleted += reactionDeletionResult.deletedCount;
            }

            // 3. Delete all comments and replies for the thread
            if (commentIdsToDelete.length > 0) {
                const commentDeletionResult = await Comments.deleteMany({ threadId: threadId });
                console.log(` - Deleted ${commentDeletionResult.deletedCount} comment(s)/replies.`);
                totalCommentsDeleted += commentDeletionResult.deletedCount;
            }

            // 4. Delete the thread itself
            const threadDeletionResult = await Threads.deleteOne({ _id: threadId });
            if (threadDeletionResult.deletedCount > 0) {
                console.log(` - Successfully deleted thread document.`);
                totalThreadsDeleted++;
            } else {
                console.log(` - Thread with ID '${threadIdString}' not found.`);
            }
        }

        console.log("\n--- Deletion Summary ---");
        console.log(`Total threads deleted: ${totalThreadsDeleted}`);
        console.log(`Total comments/replies deleted: ${totalCommentsDeleted}`);
        console.log(`Total reactions deleted: ${totalReactionsDeleted}`);
        console.log("Script finished successfully.");

    } catch (e) {
        console.error("\nAn error occurred during script execution:", e);
        process.exit(1);
    } finally {
        if (mongoClient) {
            await mongoService.onModuleDestroy();
            console.log("\nDatabase connection closed.");
        }
    }
}

removeThreads(); 