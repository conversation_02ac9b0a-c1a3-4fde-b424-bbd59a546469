generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["health", "identity", "public", "user_vault"]
}

model users {
  id                       String          @id @default(cuid())
  firebaseUid              String          @unique
  email                    String          @unique
  displayName              String?
  firstName                String?
  lastName                 String?
  birthdate                DateTime?
  gender                   String?
  language                 String?
  emailVerified            Boolean         @default(false)
  photoURL                 String?
  profileImageLargeUrl     String?
  profileImageThumbnailUrl String?
  onboardingCompleted      Boolean         @default(false)
  settings                 Json?           @default("{}") @db.JsonB
  creationTime             DateTime?
  lastSignInTime           DateTime?
  createdAt                DateTime        @default(now())
  updatedAt                DateTime        @updatedAt
  countryCode              String?
  countryName              String?
  consents                 user_consents[]
  pushTokens               push_tokens[]

  @@schema("identity")
}

model push_tokens {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@schema("identity")
}

/// Audit-log of individual user consents
model user_consents {
  id        String      @id @default(cuid())
  userId    String
  type      ConsentType
  version   String
  granted   Boolean
  timestamp DateTime    @default(now())
  ipAddress String?
  userAgent String?
  metadata  Json?
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  user      users       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, type])
  @@schema("identity")
}

model user_pseudonyms {
  id          String   @id @default(cuid())
  userId      String   @unique
  pseudonymId String   @unique @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@schema("user_vault")
}

model Disease {
  id                  String               @id @default(cuid())
  name                String               @unique
  description         String?
  icdCode             String?
  parentId            String?
  primaryCommunityId  String?              @unique
  isActive            Boolean              @default(true)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  displayName         String?
  userDiseaseProfiles UserDiseaseProfile[]
  communities         Community[]          @relation("CommunityDiseaseGroup")
  parent              Disease?             @relation("DiseaseHierarchy", fields: [parentId], references: [id])
  subDiseases         Disease[]            @relation("DiseaseHierarchy")
  primaryCommunity    Community?           @relation("PrimaryCommunityForDisease", fields: [primaryCommunityId], references: [id], onUpdate: NoAction)
  symptoms            DiseaseSymptom[]

  @@index([name])
  @@index([icdCode])
  @@schema("public")
}

model Symptom {
  id          String           @id @default(cuid())
  name        String           @unique
  description String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  diseases    DiseaseSymptom[]

  @@schema("public")
}

model DiseaseSymptom {
  diseaseId  String
  symptomId  String
  assignedAt DateTime @default(now())
  disease    Disease  @relation(fields: [diseaseId], references: [id], onDelete: Cascade)
  symptom    Symptom  @relation(fields: [symptomId], references: [id], onDelete: Cascade)

  @@id([diseaseId, symptomId])
  @@schema("public")
}

model Community {
  id                String                @id @default(cuid())
  name              String                @unique
  description       String?
  diseaseGroupId    String
  isActive          Boolean               @default(true)
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  displayName       String?
  memberships       CommunityMembership[]
  diseaseGroup      Disease               @relation("CommunityDiseaseGroup", fields: [diseaseGroupId], references: [id])
  primaryForDisease Disease?              @relation("PrimaryCommunityForDisease")

  @@index([diseaseGroupId])
  @@schema("public")
}

model MedicationMaster {
  id              String           @id(map: "medication_master_pkey")
  isActive        Boolean          @default(true)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  genericName     String           @unique(map: "medication_master_genericName_key")
  brandNames      String[]
  localizedNames  Json?
  atcCode         String?
  drugClass       String?
  drugType        String?
  userMedications UserMedication[]

  @@index([genericName], map: "medication_master_genericName_idx")
  @@schema("public")
}

model MedicalDevice {
  id                 String              @id(map: "medical_device_pkey")
  genericName        String              @unique(map: "medical_device_genericName_key")
  brandNames         String[]
  localizedNames     Json?
  deviceClass        String?
  description        String?
  isActive           Boolean             @default(true)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  userMedicalDevices UserMedicalDevice[]

  @@index([deviceClass], map: "medical_device_deviceClass_idx")
  @@index([genericName], map: "medical_device_genericName_idx")
  @@schema("public")
}

model CommunityMembership {
  id            String              @id @default(cuid())
  user_healthID String
  communityId   String
  memberRole    CommunityMemberRole @default(MEMBER)
  joinedAt      DateTime            @default(now())
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  community     Community           @relation(fields: [communityId], references: [id], onDelete: Cascade)

  @@unique([user_healthID, communityId])
  @@index([user_healthID])
  @@index([communityId])
  @@schema("health")
}

model UserDiseaseProfile {
  id              String           @id @default(cuid())
  user_healthID   String
  diseaseId       String
  userRole        UserRole
  isPrimary       Boolean          @default(false)
  diagnosisDate   DateTime?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  disease         Disease          @relation(fields: [diseaseId], references: [id])
  userMedications UserMedication[]

  @@unique([user_healthID, diseaseId])
  @@index([user_healthID])
  @@index([diseaseId])
  @@schema("health")
}

model UserMedication {
  id                   String              @id @default(cuid())
  user_healthID        String
  medicationMasterId   String
  userDiseaseProfileId String?
  dosage               String?
  frequency            String?
  notes                String?
  startDate            DateTime?
  endDate              DateTime?
  reasonForStopping    String?
  isCurrent            Boolean             @default(true)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  medicationMaster     MedicationMaster    @relation(fields: [medicationMasterId], references: [id])
  userDiseaseProfile   UserDiseaseProfile? @relation(fields: [userDiseaseProfileId], references: [id])

  @@index([user_healthID])
  @@index([userDiseaseProfileId])
  @@index([medicationMasterId])
  @@schema("health")
}

model QualityOfLifeLog {
  id            String   @id @default(cuid())
  user_healthID String
  score         Int
  notes         String?
  timestamp     DateTime @default(now())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([user_healthID, timestamp])
  @@schema("health")
}

model UserMedicalDevice {
  user_healthID   String
  medicalDeviceId String
  notes           String?
  startDate       DateTime?
  endDate         DateTime?
  isCurrent       Boolean       @default(true)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  medicalDevice   MedicalDevice @relation(fields: [medicalDeviceId], references: [id])

  @@id([user_healthID, medicalDeviceId])
  @@index([user_healthID])
  @@index([medicalDeviceId])
  @@schema("health")
}

enum UserRole {
  DIAGNOSED
  UNDIAGNOSED
  CAREGIVER

  @@schema("health")
}

enum CommunityMemberRole {
  MEMBER
  MODERATOR
  ADMIN

  @@schema("health")
}

enum ConsentType {
  TERMS_OF_SERVICE
  PRIVACY_POLICY
  DATA_SHARING
  MARKETING

  @@schema("identity")
}
