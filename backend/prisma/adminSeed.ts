import { PrismaClient } from '@prisma/client';

// Use production database URL
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.PROD_DATABASE_URL
    }
  }
});

async function seedAdminData() {
  console.log('🌱 Seeding admin data to PRODUCTION database...');

  if (!process.env.PROD_DATABASE_URL) {
    throw new Error('❌ PROD_DATABASE_URL environment variable is not set');
  }

  console.log('🔗 Using database URL:', process.env.PROD_DATABASE_URL.replace(/:[^:]*@/, ':***@'));

  try {
    // Create admin disease
    const adminDisease = await prisma.disease.upsert({
      where: { id: 'adminDisease' },
      update: {},
      create: {
        id: 'adminDisease',
        name: 'Admin Disease',
        displayName: 'Admin Disease',
        description: 'Administrative disease for system management and general discussions',
        isActive: true,
      },
    });

    console.log('✅ Admin disease created:', adminDisease.name);

    // Create admin community linked to the disease
    const adminCommunity = await prisma.community.upsert({
      where: { id: 'adminCommunity' },
      update: {},
      create: {
        id: 'adminCommunity',
        name: 'Admin Community',
        displayName: 'Admin Community',
        description: 'Administrative community for system management and general discussions',
        diseaseGroupId: 'adminDisease', // Link to the admin disease
        isActive: true,
      },
    });

    console.log('✅ Admin community created:', adminCommunity.name);

    // Update the disease to set this community as its primary community
    await prisma.disease.update({
      where: { id: 'adminDisease' },
      data: {
        primaryCommunityId: 'adminCommunity',
      },
    });

    console.log('✅ Disease updated with primary community');

    console.log('🎉 Admin data seeding completed successfully on PRODUCTION database!');
  } catch (error) {
    console.error('❌ Error seeding admin data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedAdminData()
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

export default seedAdminData; 