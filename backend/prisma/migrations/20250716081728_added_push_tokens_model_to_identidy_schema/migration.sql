-- AlterTable
ALTER TABLE "identity"."users" ADD COLUMN     "settings" J<PERSON>NB DEFAULT '{}';

-- CreateTable
CREATE TABLE "identity"."push_tokens" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "push_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "push_tokens_token_key" ON "identity"."push_tokens"("token");

-- CreateIndex
CREATE INDEX "push_tokens_userId_idx" ON "identity"."push_tokens"("userId");

-- AddForeignKey
ALTER TABLE "identity"."push_tokens" ADD CONSTRAINT "push_tokens_userId_fkey" FOREI<PERSON><PERSON> KEY ("userId") REFERENCES "identity"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
