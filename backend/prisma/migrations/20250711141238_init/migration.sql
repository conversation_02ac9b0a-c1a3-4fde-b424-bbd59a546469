-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "health";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "identity";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "user_vault";

-- CreateEnum
CREATE TYPE "health"."UserRole" AS ENUM ('DIAGNOSED', 'UNDIAGNOSED', 'CAREGIVER');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "health"."CommunityMemberRole" AS ENUM ('MEMBER', 'MODERATOR', 'ADMIN');

-- CreateEnum
CREATE TYPE "identity"."ConsentType" AS ENUM ('TERMS_OF_SERVICE', 'PRIVACY_POLICY', 'DATA_SHARING', 'MARKETING');

-- CreateTable
CREATE TABLE "identity"."users" (
    "id" TEXT NOT NULL,
    "firebaseUid" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "displayName" TEXT,
    "firstName" TEXT,
    "lastName" TEXT,
    "birthdate" TIMESTAMP(3),
    "gender" TEXT,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "photoURL" TEXT,
    "profileImageLargeUrl" TEXT,
    "profileImageThumbnailUrl" TEXT,
    "onboardingCompleted" BOOLEAN NOT NULL DEFAULT false,
    "creationTime" TIMESTAMP(3),
    "lastSignInTime" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "countryCode" TEXT,
    "countryName" TEXT,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "identity"."user_consents" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "identity"."ConsentType" NOT NULL,
    "version" TEXT NOT NULL,
    "granted" BOOLEAN NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_consents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_vault"."user_pseudonyms" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "pseudonymId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_pseudonyms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Disease" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "icdCode" TEXT,
    "parentId" TEXT,
    "primaryCommunityId" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "displayName" TEXT,

    CONSTRAINT "Disease_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Symptom" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Symptom_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."DiseaseSymptom" (
    "diseaseId" TEXT NOT NULL,
    "symptomId" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DiseaseSymptom_pkey" PRIMARY KEY ("diseaseId","symptomId")
);

-- CreateTable
CREATE TABLE "public"."Community" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "diseaseGroupId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "displayName" TEXT,

    CONSTRAINT "Community_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."MedicationMaster" (
    "id" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "genericName" TEXT NOT NULL,
    "brandNames" TEXT[],
    "localizedNames" JSONB,
    "atcCode" TEXT,
    "drugClass" TEXT,
    "drugType" TEXT,

    CONSTRAINT "medication_master_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."MedicalDevice" (
    "id" TEXT NOT NULL,
    "genericName" TEXT NOT NULL,
    "brandNames" TEXT[],
    "localizedNames" JSONB,
    "deviceClass" TEXT,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "medical_device_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "health"."CommunityMembership" (
    "id" TEXT NOT NULL,
    "user_healthID" TEXT NOT NULL,
    "communityId" TEXT NOT NULL,
    "memberRole" "health"."CommunityMemberRole" NOT NULL DEFAULT 'MEMBER',
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CommunityMembership_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "health"."UserDiseaseProfile" (
    "id" TEXT NOT NULL,
    "user_healthID" TEXT NOT NULL,
    "diseaseId" TEXT NOT NULL,
    "userRole" "health"."UserRole" NOT NULL,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "diagnosisDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserDiseaseProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "health"."UserMedication" (
    "id" TEXT NOT NULL,
    "user_healthID" TEXT NOT NULL,
    "medicationMasterId" TEXT NOT NULL,
    "userDiseaseProfileId" TEXT,
    "dosage" TEXT,
    "frequency" TEXT,
    "notes" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "reasonForStopping" TEXT,
    "isCurrent" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserMedication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "health"."QualityOfLifeLog" (
    "id" TEXT NOT NULL,
    "user_healthID" TEXT NOT NULL,
    "score" INTEGER NOT NULL,
    "notes" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "QualityOfLifeLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "health"."UserMedicalDevice" (
    "user_healthID" TEXT NOT NULL,
    "medicalDeviceId" TEXT NOT NULL,
    "notes" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "isCurrent" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserMedicalDevice_pkey" PRIMARY KEY ("user_healthID","medicalDeviceId")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_firebaseUid_key" ON "identity"."users"("firebaseUid");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "identity"."users"("email");

-- CreateIndex
CREATE INDEX "user_consents_userId_type_idx" ON "identity"."user_consents"("userId", "type");

-- CreateIndex
CREATE UNIQUE INDEX "user_pseudonyms_userId_key" ON "user_vault"."user_pseudonyms"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "user_pseudonyms_pseudonymId_key" ON "user_vault"."user_pseudonyms"("pseudonymId");

-- CreateIndex
CREATE UNIQUE INDEX "Disease_name_key" ON "public"."Disease"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Disease_primaryCommunityId_key" ON "public"."Disease"("primaryCommunityId");

-- CreateIndex
CREATE INDEX "Disease_name_idx" ON "public"."Disease"("name");

-- CreateIndex
CREATE INDEX "Disease_icdCode_idx" ON "public"."Disease"("icdCode");

-- CreateIndex
CREATE UNIQUE INDEX "Symptom_name_key" ON "public"."Symptom"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Community_name_key" ON "public"."Community"("name");

-- CreateIndex
CREATE INDEX "Community_diseaseGroupId_idx" ON "public"."Community"("diseaseGroupId");

-- CreateIndex
CREATE UNIQUE INDEX "medication_master_genericName_key" ON "public"."MedicationMaster"("genericName");

-- CreateIndex
CREATE INDEX "medication_master_genericName_idx" ON "public"."MedicationMaster"("genericName");

-- CreateIndex
CREATE UNIQUE INDEX "medical_device_genericName_key" ON "public"."MedicalDevice"("genericName");

-- CreateIndex
CREATE INDEX "medical_device_deviceClass_idx" ON "public"."MedicalDevice"("deviceClass");

-- CreateIndex
CREATE INDEX "medical_device_genericName_idx" ON "public"."MedicalDevice"("genericName");

-- CreateIndex
CREATE INDEX "CommunityMembership_user_healthID_idx" ON "health"."CommunityMembership"("user_healthID");

-- CreateIndex
CREATE INDEX "CommunityMembership_communityId_idx" ON "health"."CommunityMembership"("communityId");

-- CreateIndex
CREATE UNIQUE INDEX "CommunityMembership_user_healthID_communityId_key" ON "health"."CommunityMembership"("user_healthID", "communityId");

-- CreateIndex
CREATE INDEX "UserDiseaseProfile_user_healthID_idx" ON "health"."UserDiseaseProfile"("user_healthID");

-- CreateIndex
CREATE INDEX "UserDiseaseProfile_diseaseId_idx" ON "health"."UserDiseaseProfile"("diseaseId");

-- CreateIndex
CREATE UNIQUE INDEX "UserDiseaseProfile_user_healthID_diseaseId_key" ON "health"."UserDiseaseProfile"("user_healthID", "diseaseId");

-- CreateIndex
CREATE INDEX "UserMedication_user_healthID_idx" ON "health"."UserMedication"("user_healthID");

-- CreateIndex
CREATE INDEX "UserMedication_userDiseaseProfileId_idx" ON "health"."UserMedication"("userDiseaseProfileId");

-- CreateIndex
CREATE INDEX "UserMedication_medicationMasterId_idx" ON "health"."UserMedication"("medicationMasterId");

-- CreateIndex
CREATE INDEX "QualityOfLifeLog_user_healthID_timestamp_idx" ON "health"."QualityOfLifeLog"("user_healthID", "timestamp");

-- CreateIndex
CREATE INDEX "UserMedicalDevice_user_healthID_idx" ON "health"."UserMedicalDevice"("user_healthID");

-- CreateIndex
CREATE INDEX "UserMedicalDevice_medicalDeviceId_idx" ON "health"."UserMedicalDevice"("medicalDeviceId");

-- AddForeignKey
ALTER TABLE "identity"."user_consents" ADD CONSTRAINT "user_consents_userId_fkey" FOREIGN KEY ("userId") REFERENCES "identity"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Disease" ADD CONSTRAINT "Disease_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."Disease"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Disease" ADD CONSTRAINT "Disease_primaryCommunityId_fkey" FOREIGN KEY ("primaryCommunityId") REFERENCES "public"."Community"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "public"."DiseaseSymptom" ADD CONSTRAINT "DiseaseSymptom_diseaseId_fkey" FOREIGN KEY ("diseaseId") REFERENCES "public"."Disease"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."DiseaseSymptom" ADD CONSTRAINT "DiseaseSymptom_symptomId_fkey" FOREIGN KEY ("symptomId") REFERENCES "public"."Symptom"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Community" ADD CONSTRAINT "Community_diseaseGroupId_fkey" FOREIGN KEY ("diseaseGroupId") REFERENCES "public"."Disease"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health"."CommunityMembership" ADD CONSTRAINT "CommunityMembership_communityId_fkey" FOREIGN KEY ("communityId") REFERENCES "public"."Community"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health"."UserDiseaseProfile" ADD CONSTRAINT "UserDiseaseProfile_diseaseId_fkey" FOREIGN KEY ("diseaseId") REFERENCES "public"."Disease"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health"."UserMedication" ADD CONSTRAINT "UserMedication_medicationMasterId_fkey" FOREIGN KEY ("medicationMasterId") REFERENCES "public"."MedicationMaster"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health"."UserMedication" ADD CONSTRAINT "UserMedication_userDiseaseProfileId_fkey" FOREIGN KEY ("userDiseaseProfileId") REFERENCES "health"."UserDiseaseProfile"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health"."UserMedicalDevice" ADD CONSTRAINT "UserMedicalDevice_medicalDeviceId_fkey" FOREIGN KEY ("medicalDeviceId") REFERENCES "public"."MedicalDevice"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
