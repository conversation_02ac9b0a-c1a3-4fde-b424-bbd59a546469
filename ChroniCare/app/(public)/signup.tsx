import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  Alert,
} from 'react-native';
import { CircleUserRound, Eye, EyeOff } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '../../scr/context/themeContext';
import { BackNextButton } from '../../scr/components/backNextButton';
import { ImageBackgroundComponent } from '../../scr/components/onboarding/imageBackground';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useSignup } from '../../scr/hooks/useSignup';

export default function Signup() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [emailBlurred, setEmailBlurred] = useState(false);
  const [passwordBlurred, setPasswordBlurred] = useState(false);
  const router = useRouter();
  const { theme } = useTheme();
  const { handleSignUp: performSignUp, isSigningUp } = useSignup();

  // Validation function
  const isFormValid = useMemo(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const passwordsMatch = password === confirmPassword;
    return (
      email.trim() !== '' &&
      emailRegex.test(email) &&
      password.length >= 6 &&
      confirmPassword.length >= 6 &&
      passwordsMatch
    );
  }, [email, password, confirmPassword]);

  // Validation errors - only show after submit attempt
  const passwordTooShort = passwordBlurred && password.length > 0 && password.length < 6;
  const passwordsDontMatch = hasAttemptedSubmit && confirmPassword.length > 0 && password !== confirmPassword;
  const emailInvalid = emailBlurred && email.length > 0 && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  // Create theme-aware StyleSheet - only recreated when theme changes
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    keyboardView: {
      flex: 1,
    },
    content: {
      flex: 1,
      gap: 20,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      paddingHorizontal: 24,
    },
    profileIcon: {
      alignSelf: 'flex-start',
    },
    title: {
      ...theme.textVariants.heading('xl', 'semibold'),
      color: theme.colors.Text.text950,
      alignSelf: 'flex-start',
    },
    
    inputContainer: {
      width: '100%',
    },
    label: {
      fontSize: 16,
      color: theme.colors.Text.text950,
    },
    required: {
      color: '#ff4757',
    },
    inputWrapper: {
      position: 'relative',
      width: '100%',
    },
    input: {
      fontSize: theme.textVariants.text('sm', 'regular').fontSize,
      color: theme.colors.Text.text950,
      paddingVertical: 12,
      paddingRight: 40,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text950,
      backgroundColor: 'transparent',
    },
    eyeIcon: {
      position: 'absolute',
      right: 0,
      top: 12,
      padding: 4,
    },
    buttonsContainer: {
      width: '100%',
    },
    loader: {
      alignSelf: 'center',
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: 14,
      marginTop: 4,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    footerText: {
      fontSize: 16,
      color: theme.colors.Text.text700,
    },
    linkText: {
      fontSize: 16,
      color: theme.colors.Primary.primary500,
      fontWeight: 'bold',
    },
  }), [theme]);

  const handleSignUp = async () => {
    setHasAttemptedSubmit(true);
    console.log('signup.tsx: handleSignUp called');
    
    if (!isFormValid) {
      Alert.alert('Error', 'Please fill in all fields correctly');
      return;
    }
    await performSignUp(email, password);
  };

  const handleBack = () => {
    router.back();
  };

  const handleNext = () => {
    handleSignUp();
  };

  const navigateToLogin = () => {
    router.push('/(public)/login');
  };

  return (
    <ImageBackgroundComponent style={styles.container}>
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <View style={styles.content}>
            {/* Profile Icon */}
            <CircleUserRound 
              size={32} 
              color={theme.colors.Text.text950}
              style={styles.profileIcon}
            />

            {/* Title */}
            <Text style={styles.title}>Create your account</Text>

            {/* Email Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                E-Mail<Text style={styles.required}>*</Text>
              </Text>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter e-mail"
                  placeholderTextColor={theme.colors.Text.text500}
                  value={email}
                  onChangeText={setEmail}
                  autoCapitalize="none"
                  keyboardType="email-address"
                  autoComplete="email"
                  onFocus={() => setEmailBlurred(false)}
                  onBlur={() => setEmailBlurred(true)}
                />
              </View>
              {emailInvalid && (
                <Text style={styles.errorText}>Please enter a valid email address</Text>
              )}
            </View>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Password<Text style={styles.required}>*</Text>
              </Text>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter password"
                  placeholderTextColor={theme.colors.Text.text500}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoComplete="new-password"
                  onFocus={() => setPasswordBlurred(false)}
                  onBlur={() => setPasswordBlurred(true)}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <Eye size={20} color={theme.colors.Text.text600} />
                  ) : (
                    <EyeOff size={20} color={theme.colors.Text.text600} />
                  )}
                </TouchableOpacity>
              </View>
              {passwordTooShort && (
                <Text style={styles.errorText}>Password must be at least 6 characters</Text>
              )}
            </View>

            {/* Confirm Password Input */}
            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={styles.input}
                  placeholder="Confirm password"
                  placeholderTextColor={theme.colors.Text.text500}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                  autoComplete="new-password"
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <Eye size={20} color={theme.colors.Text.text600} />
                  ) : (
                    <EyeOff size={20} color={theme.colors.Text.text600} />
                  )}
                </TouchableOpacity>
              </View>
              {passwordsDontMatch && (
                <Text style={styles.errorText}>Passwords do not match</Text>
              )}
            </View>

            {isSigningUp && (
              <ActivityIndicator
                size="large"
                color={theme.colors.Primary.primary500}
                style={styles.loader}
              />
            )}

            {/* Back/Next Buttons */}
            <View style={styles.buttonsContainer}>
              <BackNextButton
                onBackPress={handleBack}
                onNextPress={handleNext}
                nextInactive={!isFormValid}
                disabled={isSigningUp}
              />
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>
                Already have an account?{' '}
              </Text>
              <TouchableOpacity onPress={navigateToLogin}>
                <Text style={styles.linkText}>
                  Log in
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackgroundComponent>
  );
}
