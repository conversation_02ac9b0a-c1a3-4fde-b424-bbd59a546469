import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  ScrollView,
  Image as RNImage,
  Platform,
  StatusBar,
} from "react-native";
import React from "react";
import { useTheme } from "../../../scr/context/themeContext";
import { XCircle, Plus, Camera, Image, X } from "lucide-react-native";
import { useRouter } from "expo-router";
import Button from "../../../scr/components/button";
import { useUser } from "../../../scr/context/userContext";
import { ActivityIndicator } from "react-native";
import { useCreateThread } from "../../../scr/hooks/useCreateThread";
import { useThreadImages } from "../../../scr/hooks/useThreadImages";
import { KeyboardAvoidingView } from "react-native-keyboard-controller";
import Label from "../../../scr/components/forms/Label";
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { hexToRgba } from "../../../scr/utils/hexToRgba";
import { processContent } from "../../../scr/utils/processContent";
import { useDraft } from "../../../scr/hooks/useDraft";

const CreatePostScreen = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const [selectedCommunity, setSelectedCommunity] = React.useState<string | null>(
    null
  );
  const [title, setTitle] = React.useState("");
  const [content, setContent] = React.useState("");
  const [showCharLimit, setShowCharLimit] = React.useState(false);
  const titleInputRef = React.useRef<TextInput>(null);
  const contentInputRef = React.useRef<TextInput>(null);

  const { user, loading: userLoading, error: userError } = useUser();
  const { createThread, loading: createLoading } = useCreateThread({
    onSuccess: async () => {
      isClosing.current = true;
      await clearDraft();
    }
  });
  const {
    uploadedImages,
    isUploading,
    uploadImage,
    selectAndUploadImage,
    removeImage
  } = useThreadImages();
  const { draft, isLoaded, saveDraft, clearDraft } = useDraft();
  const isClosing = React.useRef(false);

  React.useEffect(() => {
    if (isLoaded && draft) {
      setTitle(draft.title || "");
      setContent(draft.content || "");
    }
  }, [isLoaded, draft]);

  React.useEffect(() => {
    return () => {
      if (isClosing.current) return;
      if (title.trim().length > 0 || content.trim().length > 0) {
        saveDraft(title, content);
      }
    };
  }, [title, content, saveDraft]);

  React.useEffect(() => {
    if (user?.communities && user.communities.length === 1) {
      setSelectedCommunity(user.communities[0].id);
    }
  }, [user]);

  React.useEffect(() => {
    // Focus the title input when component mounts
    const timeout = setTimeout(() => {
      titleInputRef.current?.focus();
    }, 100);
    return () => clearTimeout(timeout);
  }, []);

  const communities = user?.communities || [];

  const isFormValid = title.trim().length > 0 && content.trim().length > 0 && !!selectedCommunity;

  const handleSubmit = () => {
    if (!isFormValid) {
      Alert.alert("Incomplete Post", "Please provide a title, content, and select a community.");
      return;
    }
    const processedTitle = title.trim();
    const processedContent = processContent(content);

    createThread(processedTitle, processedContent, selectedCommunity, uploadedImages);
  };

  const handleClose = () => {
    if (title.trim().length === 0 && content.trim().length === 0) {
      router.back();
      return;
    }

    Alert.alert(
      "Save as a draft?",
      "You can save this post as a draft and come back to it later.",
      [
        {
          text: "Save to drafts",
          onPress: async () => {
            isClosing.current = true;
            await saveDraft(title, content);
            router.back();
          },
        },
        {
          text: "Don't save",
          style: "destructive",
          onPress: async () => {
            isClosing.current = true;
            await clearDraft();
            router.back();
          },
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ]
    );
  };

  const handleCameraPress = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.8,
        base64: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const mimeType = asset.mimeType || 'image/jpeg';
        await uploadImage(asset.uri, mimeType);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
    },
    innerContainer: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight? StatusBar.currentHeight + 20 : 32) : 32,
    },
    topNavBar: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.spacing.s1,
      marginBottom: theme.spacing.spacing.s4,
    },
    titleInput: {
      fontFamily: theme.typography.fontFamily.merryweather,
      fontSize: theme.typography.headingSize.lg,
      color: theme.colors.Text.text900,
      paddingBottom: theme.spacing.spacing.s2,
    },
    contentWrapper: {
      flex: 1,
      paddingBottom: 30,
    },
    contentInput: {
      fontFamily: theme.typography.fontFamily.inter,
      fontSize: theme.typography.textSize.sm,
      color: theme.colors.Text.text900,
      textAlignVertical: "top",
      minHeight: 100,
    },
    optionRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginVertical: theme.spacing.spacing.s2,
    },
    optionText: {
      fontFamily: theme.typography.fontFamily.interMedium,
      fontSize: theme.typography.textSize.md,
      color: theme.colors.Text.text900,
    },
    communitySelectionContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      alignItems: "center",
      justifyContent: "flex-end",
      flex: 1,
      marginLeft: theme.spacing.spacing.s8,
    },
    communityBadge: {
      paddingVertical: theme.spacing.spacing.s1,
      paddingHorizontal: theme.spacing.spacing.s2,
      borderRadius: theme.spacing.borderRadius.xl2,
      margin: theme.spacing.spacing.s0_5,
    },
    communityBadgeText: {
      fontFamily: theme.typography.fontFamily.inter,
      fontSize: theme.typography.textSize.sm,
    },
    errorText: {
        color: theme.colors.Secondary.secondary500,
        textAlign: 'center',
        marginTop: theme.spacing.spacing.s2,
    },
    bottomBar: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      justifyContent: 'space-between',
      padding: theme.spacing.spacing.s4,
      backgroundColor: theme.colors.Background.background0,
    },
    communitySection: {
      flex: 1,
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    communityText: {
      fontFamily: theme.typography.fontFamily.inter,
      fontSize: theme.typography.textSize.sm,
      color: theme.colors.Text.text700,
      marginBottom: theme.spacing.spacing.s2,
    },
    communityPillsContainer: {
      flexDirection: 'row',
      gap: theme.spacing.spacing.s2,
    },
    createButtonContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingLeft: theme.spacing.spacing.s1,
    },
    iconContainer: {
      flexDirection: "row",
      paddingVertical: theme.spacing.spacing.s2,
    },
    icon: {
      marginRight: theme.spacing.spacing.s4,
    },
    imageContainer: {
      marginVertical: theme.spacing.spacing.s3,
    },
    imagePreview: {
      width: 120,
      height: 80,
      borderRadius: theme.spacing.borderRadius.md,
      marginRight: theme.spacing.spacing.s2,
      marginBottom: theme.spacing.spacing.s2,
      marginTop: theme.spacing.spacing.s2,
      position: 'relative',
    },
    imagePreviewContent: {
      width: '100%',
      height: '100%',
      borderRadius: theme.spacing.borderRadius.md,
    },
    removeImageButton: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: theme.colors.Secondary.secondary500,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    imagesScrollContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    charLimitText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: theme.typography.textSize.sm,
      fontFamily: theme.typography.fontFamily.inter,
      marginTop: theme.spacing.spacing.s1,
    },
    gradientOverlay: {
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      width: 50, // Adjust width as needed
      pointerEvents: 'none', // Allows scroll interaction
    },
  });


  return (
    <SafeAreaView style={styles.container}>
    <KeyboardAvoidingView style={styles.container} behavior={Platform.OS === 'ios' ? 'padding' : 'height'} keyboardVerticalOffset={70}>
        <View style={styles.innerContainer}>
          <View style={styles.topNavBar}>
            <TouchableOpacity onPress={handleClose}>
              <XCircle size={24} color={theme.colors.Text.text950} />
            </TouchableOpacity>
          </View>
          <TextInput
            ref={titleInputRef}
            placeholder="Title"
            placeholderTextColor={theme.colors.Text.text500}
            multiline={true}
            style={styles.titleInput}
            value={title}
            onChangeText={(text) => {
              const sanitizedText = text.replace(/\n/g, "");
              if (sanitizedText.length <= 300) {
                setTitle(sanitizedText);
                setShowCharLimit(sanitizedText.length === 300);
              }
            }}
            maxLength={300}
            returnKeyType="next"
            submitBehavior="blurAndSubmit"
            onSubmitEditing={() => contentInputRef.current?.focus()}
          />
          {showCharLimit && (
            <Text style={styles.charLimitText}>
              Maximum character limit of 300 reached
            </Text>
          )}
          <View style={styles.contentWrapper}>
              <TextInput
                ref={contentInputRef}
                placeholder="Body text"
                placeholderTextColor={theme.colors.Text.text500}
                style={styles.contentInput}
                multiline
                value={content}
                onChangeText={setContent}
                maxLength={3000}
              />
            
            {/* Image Preview Section */}
            {uploadedImages.length > 0 && (
              <View style={styles.imageContainer}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.imagesScrollContainer}>
                    {uploadedImages.map((imageUrl, index) => (
                      <View key={index} style={styles.imagePreview}>
                        <RNImage
                          source={{ uri: imageUrl }}
                          style={styles.imagePreviewContent}
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          style={styles.removeImageButton}
                          onPress={() => removeImage(imageUrl)}
                        >
                          <X size={16} color={theme.colors.Background.background0} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}

            <View style={styles.iconContainer}>
              <TouchableOpacity 
                style={styles.icon} 
                onPress={handleCameraPress}
                disabled={isUploading}
              >
                {isUploading ? (
                  <ActivityIndicator size="small" color={theme.colors.Text.text500} />
                ) : (
                  <Camera size={24} color={theme.colors.Text.text500} />
                )}
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.icon} 
                onPress={selectAndUploadImage}
                disabled={isUploading}
              >
                <Image size={24} color={theme.colors.Text.text500} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View style={styles.bottomBar}>
          <View style={styles.communitySection}>
            <Text style={styles.communityText}>
              {communities.length === 1 ? 'Posting in:' : 'Select a community'}
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.communityPillsContainer}>
                {communities.map((community) => (
                  <Label
                    key={community.id}
                    text={community.displayName}
                    variant={
                      selectedCommunity === community.id ? "solid" : "outline"
                    }
                    colorScheme={
                      selectedCommunity === community.id ? "primary" : "muted"
                    }
                    onPress={() => setSelectedCommunity(community.id)}
                  />
                ))}
              </View>
            </ScrollView>
            <LinearGradient
              colors={[hexToRgba(theme.colors.Background.background0, 0), theme.colors.Background.background0]}
              style={styles.gradientOverlay}
              start={{ x: 0, y: 0.5 }}
              end={{ x: 1, y: 0.5 }}
            />
          </View>
          <View style={styles.createButtonContainer}>
            <Button
              title="Post"
              onPress={handleSubmit}
              size="small"
              variant="iconNeutral"
              backgroundColor={theme.colors.Primary.primary500}
              icon={createLoading || isUploading ? <ActivityIndicator color={theme.colors.Background.background0}/> : <Plus size={20} color={theme.colors.Background.background0} />}
              iconPosition="right"
              disabled={!isFormValid || createLoading || isUploading}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default CreatePostScreen;