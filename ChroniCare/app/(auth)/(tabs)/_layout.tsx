import { View, Text, Image } from 'react-native'
import React, { useState, useEffect } from 'react'
import { Ta<PERSON>, useRouter } from 'expo-router'
import { useTheme } from '../../../scr/context/themeContext'
import * as Haptics from 'expo-haptics'
import { UsersRound, CirclePlus, HeartPulse } from 'lucide-react-native'
import AppHeader from '@/scr/components/global/AppHeader'
import FeedbackButton from '@/scr/components/global/feedbackButton'
import FeedbackModal from '@/scr/components/global/feedbackModal'
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet'
import { NotificationsPermissionSheet } from '@/scr/components/global/NotificationsPermissionSheet'
import { useNotifications } from '@/scr/hooks/useNotifications'
import { notificationPromptUtils } from '@/scr/utils/notificationPromptUtils'
import { PushTokenDebug } from '@/scr/components/debug/PushTokenDebug'

const _layout = () => {
  const theme = useTheme();
  const router = useRouter();
  const [isFeedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [showNotificationsSheet, setShowNotificationsSheet] = useState(false);
  const { requestPermissions } = useNotifications();

  // Check if user has seen the notifications prompt
  useEffect(() => {
    const checkNotificationsPrompt = async () => {
      try {
        const hasSeenPrompt = await notificationPromptUtils.hasSeenPrompt();
        if (!hasSeenPrompt) {
          // Show the sheet after a short delay to ensure the layout is ready
          setTimeout(() => {
            setShowNotificationsSheet(true);
          }, 1000);
        }
      } catch (error) {
        console.error('Error checking notifications prompt flag:', error);
      }
    };

    checkNotificationsPrompt();
  }, []);

  const handleActivateNotifications = async () => {
    try {
      await requestPermissions();
      await notificationPromptUtils.markPromptAsSeen();
      setShowNotificationsSheet(false);
    } catch (error) {
      console.error('Error activating notifications:', error);
      // Still mark as seen even if there was an error
      await notificationPromptUtils.markPromptAsSeen();
      setShowNotificationsSheet(false);
    }
  };

  const handleDismissNotifications = async () => {
    try {
      await notificationPromptUtils.markPromptAsSeen();
      setShowNotificationsSheet(false);
    } catch (error) {
      console.error('Error saving notifications prompt flag:', error);
      setShowNotificationsSheet(false);
    }
  };

  return (
    <BottomSheetModalProvider>
      <View style={{ flex: 1, backgroundColor: theme.theme.colors.Background.background0 }}>
        <Tabs
    screenOptions={{
        tabBarShowLabel: false,
        tabBarInactiveTintColor: theme.theme.colors.Text.text900,
        tabBarStyle: {
            backgroundColor: theme.theme.colors.Background.background0,
        },
        headerStyle: {
            backgroundColor: theme.theme.colors.Background.background0,
        },
        headerShadowVisible: false,
        headerTitle: () => null,
    }}
    >
        

        <Tabs.Screen name="community" options={{
            headerShown: false,
            tabBarIcon: ({ color, size, focused }) => (
                <UsersRound color={color} size={size} />
            )
        }} />
        <Tabs.Screen name="create" options={{
        headerShown: false,
        title: 'Create',
        tabBarIcon: ({ color, size, focused }) => (
            <CirclePlus color={color} size={size} />
        )
        }}
        listeners={{
            tabPress: (e) => {
                e.preventDefault();
                Haptics.selectionAsync();
                router.push('/(auth)/(modal)/create');
            }
        }} />
       <Tabs.Screen name="journey" options={{
        header: () => <AppHeader />,
        tabBarIcon: ({ color, size, focused }) => (
            <HeartPulse color={color} size={size} />
        )}} />
      
        </Tabs>
        <FeedbackButton onPress={() => setFeedbackModalVisible(true)} />
        <FeedbackModal
            visible={isFeedbackModalVisible}
            onClose={() => setFeedbackModalVisible(false)}
        />

        <NotificationsPermissionSheet
          isVisible={showNotificationsSheet}
          onActivate={handleActivateNotifications}
          onDismiss={handleDismissNotifications}
        />
      </View>
      {/* <PushTokenDebug /> */}
    </BottomSheetModalProvider>
  )
}

export default _layout