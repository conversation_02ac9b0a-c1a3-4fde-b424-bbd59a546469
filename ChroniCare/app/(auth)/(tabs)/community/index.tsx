import React from "react";
import { View, Text, StyleSheet, ActivityIndicator, RefreshControl, ScrollView, Alert, TouchableOpacity, Animated } from "react-native";
import { useMemo, useCallback, useState } from "react";
import { useTheme } from "@/scr/context/themeContext";
import Label from "@/scr/components/forms/Label";
import { useQuery, ApolloError, useApolloClient } from "@apollo/client";
import { GET_THREADS } from "@/scr/graphql/queries";
import ThreadCard from "@/scr/components/community/thread/threadCard";
import { useUser } from "@/scr/context/userContext";
import { FlashList } from "@shopify/flash-list";
import { useLocalSearchParams, useRouter, useFocusEffect } from "expo-router";
import { Thread } from "@/scr/graphql/fragments";
import { Minus, Plus } from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { hexToRgba } from "@/scr/utils/hexToRgba";
import { useNotification } from "@/scr/context/notificationsContext";


const ThemedDivider = () => {
  const { theme } = useTheme();
  return <View style={{ height: 1, backgroundColor: theme.colors.Background.background100 }} />;
}

const CommunityScreen = () => {
  const { theme } = useTheme();
  const { user } = useUser();
  const router = useRouter();
  const client = useApolloClient();
  const { newlyFetchedThread, clearNewlyFetchedThread } = useNotification();
  const params = useLocalSearchParams<{ communityIds?: string }>();
  const [refreshing, setRefreshing] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const fadeAnim = React.useRef(new Animated.Value(1)).current;
  
  const selectedCommunityIds = useMemo(() => {
    if (params.communityIds) {
      return params.communityIds.split(',');
    }
    return user?.communities?.map(c => c.id) || [];
  }, [params.communityIds, user?.communities]);



  const isNetworkError = (error: ApolloError): boolean => {
    return error.networkError !== null || 
           error.message?.includes('Network error') ||
           error.message?.includes('Failed to fetch') ||
           error.message?.includes('network') ||
           error.graphQLErrors?.some(err => err.extensions?.code === 'NETWORK_ERROR') ||
           false;
  };

  const { data, loading, error, refetch, fetchMore } = useQuery<{ threads: Thread[] }>(GET_THREADS, {
    variables: {
      limit: 6,
      communityIds: selectedCommunityIds.length > 0 ? selectedCommunityIds : undefined,
      cursor: null,
    },
    skip: selectedCommunityIds.length === 0,
    notifyOnNetworkStatusChange: true,
    // Use cache-first for smoother transitions
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    // This ensures the query refetches when variables change
    nextFetchPolicy: 'cache-first',
  });

  // Effect to add newly fetched thread to the cache from notifications
  useFocusEffect(
    useCallback(() => {
      if (newlyFetchedThread) {
        client.cache.updateQuery({ 
          query: GET_THREADS, 
          variables: {
            limit: 6,
            communityIds: selectedCommunityIds.length > 0 ? selectedCommunityIds : undefined,
            cursor: null,
          } 
        }, (data) => {
          if (data && data.threads.every((t: Thread) => t._id !== newlyFetchedThread._id)) {
            return {
              ...data,
              threads: [newlyFetchedThread, ...data.threads],
            };
          }
          return data;
        });
        clearNewlyFetchedThread();
      }
    }, [newlyFetchedThread, client.cache, clearNewlyFetchedThread, selectedCommunityIds])
  );

  // Effect to fetch newer threads when the screen is focused
  useFocusEffect(
    useCallback(() => {
      const fetchNewerThreads = async () => {
        if (data?.threads && data.threads.length > 0) {
          const newestThreadTimestamp = data.threads[0].createdAt;
          
          await fetchMore({
            variables: {
              since: newestThreadTimestamp,
              communityIds: selectedCommunityIds.length > 0 ? selectedCommunityIds : undefined,
            },
            updateQuery: (prevResult, { fetchMoreResult }) => {
              if (!fetchMoreResult || !fetchMoreResult.threads || fetchMoreResult.threads.length === 0) {
                return prevResult;
              }
              
              const existingIds = new Set(prevResult.threads.map(thread => thread._id));
              const newThreads = fetchMoreResult.threads.filter(
                newThread => !existingIds.has(newThread._id)
              );

              if (newThreads.length > 0) {
                return {
                  threads: [...newThreads, ...prevResult.threads],
                };
              }

              return prevResult;
            },
          });
        }
      };

      fetchNewerThreads();
    }, [data, fetchMore, selectedCommunityIds])
  );


  // Smooth transition when selectedCommunityIds changes
  const prevCommunityIds = React.useRef<string[]>([]);
  React.useEffect(() => {
    const currentIds = selectedCommunityIds.sort();
    const prevIds = prevCommunityIds.current.sort();
    
    if (JSON.stringify(currentIds) !== JSON.stringify(prevIds)) {
      prevCommunityIds.current = selectedCommunityIds;
      if (selectedCommunityIds.length > 0) {
        setIsTransitioning(true);
        
        // Subtle fade animation
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 0.7,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();

        refetch({
          limit: 8,
          communityIds: selectedCommunityIds,
          cursor: null,
        }).finally(() => {
          setIsTransitioning(false);
        });
      }
    }
  }, [selectedCommunityIds, refetch, fadeAnim]);

  const threadList: Thread[] = useMemo(() => data?.threads || [], [data?.threads]);

  
  const loadMore = useCallback(async () => {
    if (isLoadingMore || !threadList.length || loading) return;
    
    setIsLoadingMore(true);
    try {
      const lastThread = threadList[threadList.length - 1];
      
      const variables = {
        limit: 8,
        communityIds: selectedCommunityIds.length > 0 ? selectedCommunityIds : undefined,
        cursor: lastThread._id,
      };
      
      await fetchMore({
        variables,
        updateQuery: (prevResult, { fetchMoreResult }) => {
          if (!fetchMoreResult || !fetchMoreResult.threads || fetchMoreResult.threads.length === 0) {
            return prevResult;
          }
          
          // Filter out duplicates based on _id
          const existingIds = new Set(prevResult.threads.map(thread => thread._id));
          const newThreads = fetchMoreResult.threads.filter(
            newThread => !existingIds.has(newThread._id)
          );
          
          return {
            threads: [...prevResult.threads, ...newThreads],
          };
        },
      });
    } catch (error) {
      console.error('Error loading more threads:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchMore, threadList, selectedCommunityIds, isLoadingMore, loading]);

  const handleRefresh = useCallback(async () => {
    if (refreshing) return;
    
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing threads:', error);
      
      // Show alert for network errors during refresh
      if (error instanceof Error && isNetworkError(error as ApolloError)) {
        Alert.alert(
          'Connection Problem',
          'Unable to refresh posts. Please check your internet connection and try again.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      setRefreshing(false);
    }
  }, [refetch, refreshing]);

  const handleCommunityPress = useCallback((communityId: string) => {
    // If only one community is selected and it's the one being clicked, do nothing
    if (selectedCommunityIds.length === 1 && selectedCommunityIds.includes(communityId)) {
      return;
    }
    
    const newSelectedIds = selectedCommunityIds.includes(communityId)
      ? selectedCommunityIds.filter(id => id !== communityId)
      : [...selectedCommunityIds, communityId];
    
    // Update URL params immediately for better UX
    router.setParams({ 
      communityIds: newSelectedIds.length > 0 ? newSelectedIds.join(',') : undefined 
    });
  }, [selectedCommunityIds, router]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      gap: theme.spacing.spacing.s4,
      backgroundColor: theme.colors.Background.background0,
    },
    threadContainer: {
      gap: theme.spacing.spacing.s2,
    },
    labelContainer: {
      flexDirection: 'row',
      gap: theme.spacing.spacing.s2,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    dividerContainer: {
      paddingBottom: theme.spacing.spacing.s4,
    },
  
    contentContainer: {
      flex: 1,
    },
    loadingMoreContainer: {
      paddingVertical: theme.spacing.spacing.s4,
      alignItems: 'center',
      justifyContent: 'center',
    },
    communitySelectorContainer: {
      position: 'relative',
    },
    gradientOverlay: {
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      width: 50,
      pointerEvents: 'none',
    },
  }), [theme]); 

  const renderContent = () => {
    // Only show loading spinner on initial load when we have no data
    if (loading && threadList.length === 0) {
      return (
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      );
    }

    // Only show error if we have no cached data and it's not a network error
    if (error && threadList.length === 0 && !isNetworkError(error)) {
      return (
        <View style={styles.centered}>
          <Text style={{ color: theme.colors.Text.text0 }}>Error: {error.message}</Text>
        </View>
      );
    }
    
    if (!loading && threadList.length === 0) {
      return (
        <ScrollView
          contentContainerStyle={styles.centered}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.Background.background900}
              colors={[theme.colors.Background.background900]}
            />
          }
        >
          <Text style={{ color: theme.colors.Text.text900 }}>
            {selectedCommunityIds.length === 0 
              ? 'Please select at least one community to view posts.' 
              : 'No posts found in selected communities.'}
          </Text>
        </ScrollView>
      )
    }

    return (
      <Animated.View style={[styles.contentContainer, { opacity: fadeAnim }]}>
        <FlashList<Thread>
          data={threadList}
          keyExtractor={(item) => item._id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.Background.background900}
              colors={[theme.colors.Background.background900]}
            />
          }
          renderItem={({ item }) => (
            <View style={styles.threadContainer}>
              <ThreadCard threadId={item._id} truncateContent={true} />
              <View style={styles.dividerContainer}>
                <ThemedDivider />
              </View>
            </View>
          )}
          estimatedItemSize={180}
          onEndReached={loadMore}
          onEndReachedThreshold={0.3}
          ListFooterComponent={
            isLoadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size="small" color={theme.colors.Primary.primary500} />
              </View>
            ) : null
          }
        />
      </Animated.View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.communitySelectorContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.labelContainer}>
            {user?.communities?.map((community) => (
              <TouchableOpacity
                key={community.id}
                onPress={() => handleCommunityPress(community.id)}
              >
                <Label
                  text={community.displayName}
                  variant={selectedCommunityIds.includes(community.id) ? "solid" : "outline"}
                  colorScheme={selectedCommunityIds.includes(community.id) ? "primary" : "muted"}
                  iconRight={selectedCommunityIds.includes(community.id) ? <Minus size={12} color={theme.colors.Text.text900} /> : <Plus size={12} color={theme.colors.Text.text900} />}
                />
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
        <LinearGradient
          colors={[hexToRgba(theme.colors.Background.background0, 0), theme.colors.Background.background0]}
          style={styles.gradientOverlay}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
        />
      </View>
      {renderContent()}
    </View>
  );
};

export default CommunityScreen;