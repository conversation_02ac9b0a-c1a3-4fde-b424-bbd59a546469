import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { useRouter } from 'expo-router'
import { useTheme } from '../../../scr/context/themeContext'

const create = () => {
  const router = useRouter()
  const { theme } = useTheme()

  React.useEffect(() => {
    // Redirect to modal create screen
    router.push('/(auth)/(modal)/create')
  }, [])

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <TouchableOpacity 
        onPress={() => router.push('/(auth)/(modal)/create')}
        style={{
          backgroundColor: theme.colors.Primary.primary500,
          padding: 16,
          borderRadius: 8,
        }}
      >
        <Text style={{ color: theme.colors.Background.background0 }}>Create Thread</Text>
      </TouchableOpacity>
    </View>
  )
}

export default create