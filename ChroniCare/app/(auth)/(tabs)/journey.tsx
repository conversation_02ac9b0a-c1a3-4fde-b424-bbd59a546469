import { View, Text, StyleSheet } from 'react-native'
import React from 'react'
import { useTheme } from '../../../scr/context/themeContext';

const journey = () => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.theme.colors.Background.background0,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.theme.spacing.spacing.s5
    },
    text: {
        color: theme.theme.colors.Text.text900,
        fontSize: theme.theme.typography.textSize.sm,
        textAlign: 'center',
        fontFamily: theme.theme.typography.fontFamily.inter,
    }
  });

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Here you will be able to track your health, log your symptoms and get insights into your health</Text>
    </View>
  )
}

export default journey