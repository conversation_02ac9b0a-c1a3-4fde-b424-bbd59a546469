import { View, StyleSheet, SafeAreaView } from 'react-native';
import React, { useMemo, useEffect } from 'react';
import { UsersRound } from 'lucide-react-native';
import { useTheme } from '../../../scr/context/themeContext';
import IconTitleInstructions from '../../../scr/components/IconTitleInstructions';
import { BackNextButton } from '@/scr/components/backNextButton';
import { router, useFocusEffect } from 'expo-router';
import ComboBox, { ComboBoxItem } from '@/scr/components/forms/ComboBox';
import { diseasesData } from '../../../scr/data/conditions';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useDiseasesStore, useOnboardingStore } from '../../../scr/utils/onboardingStore';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';

const Disease = () => {
  const { theme } = useTheme();
  
  // Use the onboarding store for disease selection management
  const { diseases, setSelectedDiseases, setDiseasesComplete, isDiseasesValid } = useDiseasesStore();
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(4); // Disease is step 4
    }, [setCurrentStep])
  );

  const handleSelectionChange = (items: ComboBoxItem[]) => {
    setSelectedDiseases(items);
  };

  const handleNext = () => {
    // Mark diseases as complete in the store
    setDiseasesComplete();
    router.push({
      pathname: '/(auth)/(onboarding)/userType/[index]',
      params: { index: 0 },
    });
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s6,
    },
  }), [theme]);

  return (
    <ImageBackgroundComponent style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
       <KeyboardAvoidingView
        behavior="padding"
        style={styles.keyboardAvoidingContainer}
        keyboardVerticalOffset={70}
       >
          <View style={styles.content}>
            <View style={styles.mainContent}>
              <View style={styles.centeredPageContentWrapper}>
                <IconTitleInstructions
                  icon={UsersRound}
                  title="Choose your support community"
                  instructions="This will help us tailor your experience and find relevant support."
                />
                <ComboBox
                  data={diseasesData}
                  labelColorScheme="muted"
                  onSelectionChange={handleSelectionChange}
                  selectedItems={diseases.selectedDiseases}
                  label="Disease communities"
                  placeholder="Search for a condition  "
                  multiple={true}
                  required={true}
                />
              </View>
              <BackNextButton
                onBackPress={() => router.back()}
                onNextPress={handleNext}
                nextTitle="Next"
                nextInactive={!isDiseasesValid()}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackgroundComponent>
  );
};

export default Disease;