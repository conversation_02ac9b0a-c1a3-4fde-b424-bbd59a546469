import { View, StyleSheet, SafeAreaView, Text } from 'react-native';
import React, { useMemo, useState, useEffect } from 'react';
import { Users } from 'lucide-react-native';
import { useTheme } from '../../../scr/context/themeContext';
import IconTitleInstructions from '../../../scr/components/IconTitleInstructions';
import { BackNextButton } from '@/scr/components/backNextButton';
import { router, useFocusEffect } from 'expo-router';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import GradientBackground from '../../../scr/components/onboarding/gradientBackground';
import SelectionButton from '../../../scr/components/forms/SelectionButton';
import { useDiseasesStore, useUserTypesStore, useOnboardingStore } from '../../../scr/utils/onboardingStore';

const UserType = () => {
  const { theme } = useTheme();
  const { diseases } = useDiseasesStore();
  const { 
    setUserTypeForDisease, 
    getUserTypeForDisease, 
    areAllUserTypesComplete,
    setUserTypesComplete,
    getDiagnosisStatusForDisease,
    hasAnyDiagnosedConditions
  } = useUserTypesStore();
  console.log('Screen: Usertype');
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(5); // UserType is step 5
    }, [setCurrentStep])
  );

  const [userSelections, setUserSelections] = useState<Record<string | number, {
    role?: 'patient' | 'caregiver';
    diagnosisStatus?: 'diagnosed' | 'not-diagnosed';
  }>>({});

  // Initialize user selections from store
  useEffect(() => {
    const initialSelections: Record<string | number, {
      role?: 'patient' | 'caregiver';
      diagnosisStatus?: 'diagnosed' | 'not-diagnosed';
    }> = {};

    diseases.selectedDiseases.forEach(disease => {
      const userType = getUserTypeForDisease(disease.id);
      initialSelections[disease.id] = {
        role: userType?.role,
        diagnosisStatus: userType?.diagnosisStatus,
      };
    });

    setUserSelections(initialSelections);
  }, [diseases.selectedDiseases, getUserTypeForDisease]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s2,
    },
    diseaseSection: {
      marginBottom: theme.spacing.spacing.s6,
    },
    diseaseName: {
      ...theme.textVariants.text('xl', 'semibold'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s3,
    },
    sectionTitle: {
      ...theme.textVariants.text('md', 'medium'),
      color: theme.colors.Text.text700,
      marginBottom: theme.spacing.spacing.s2,
    },
    selectionContainer: {
      gap: theme.spacing.spacing.s2,
      marginBottom: theme.spacing.spacing.s4,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.Background.background200,
      marginVertical: theme.spacing.spacing.s4,
    },
  }), [theme]);

  const handleRoleSelection = (diseaseId: string | number, role: 'patient' | 'caregiver') => {
    const newSelections = { ...userSelections };
    newSelections[diseaseId] = {
      ...newSelections[diseaseId],
      role,
      // Clear diagnosis status if switching from patient to caregiver
      diagnosisStatus: role === 'caregiver' ? undefined : newSelections[diseaseId]?.diagnosisStatus,
    };
    setUserSelections(newSelections);

    // Update store
    if (role === 'caregiver') {
      setUserTypeForDisease(diseaseId, role);
    } else {
      // For patients, we need diagnosis status
      const diagnosisStatus = newSelections[diseaseId]?.diagnosisStatus;
      if (diagnosisStatus) {
        setUserTypeForDisease(diseaseId, role, diagnosisStatus);
      }
    }
  };

  const handleDiagnosisStatusSelection = (diseaseId: string | number, diagnosisStatus: 'diagnosed' | 'not-diagnosed') => {
    const newSelections = { ...userSelections };
    newSelections[diseaseId] = {
      ...newSelections[diseaseId],
      diagnosisStatus,
    };
    setUserSelections(newSelections);

    // Update store
    const role = newSelections[diseaseId]?.role;
    if (role === 'patient') {
      setUserTypeForDisease(diseaseId, role, diagnosisStatus);
    }
  };

  const isSelectionComplete = () => {
    return diseases.selectedDiseases.every(disease => {
      const selection = userSelections[disease.id];
      if (!selection?.role) return false;
      
      // If role is caregiver, we're done
      if (selection.role === 'caregiver') return true;
      
      // If role is patient, we need diagnosis status
      return selection.role === 'patient' && selection.diagnosisStatus !== undefined;
    });
  };

  const handleBack = () => {
    router.back();
  };

  const handleNext = () => {
    // Mark user types as complete
    if (areAllUserTypesComplete()) {
      setUserTypesComplete();
    }

    // Check if we need to go to medication page using store helper
    if (hasAnyDiagnosedConditions()) {
      router.push('/(auth)/(onboarding)/medication');
    } else {
      // Complete onboarding and go to main app
      router.replace('/');
    }
  };

  if (diseases.selectedDiseases.length === 0) {
    router.back();
    return null;
  }

  return (
    <GradientBackground style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior="padding"
          style={styles.keyboardAvoidingContainer}
        >
          <View style={styles.content}>
            <View style={styles.mainContent}>
              <View style={styles.centeredPageContentWrapper}>
                <IconTitleInstructions
                  icon={Users}
                  title="Your relationship to the condition"
                  instructions="For each condition, tell us your relationship to it."
                />

                {diseases.selectedDiseases.map((disease, index) => (
                  <View key={disease.id}>
                    <View style={styles.diseaseSection}>
                      <Text style={styles.diseaseName}>
                        {disease.label}
                      </Text>

                      <Text style={styles.sectionTitle}>
                        What is your relationship to this condition?
                      </Text>
                      <View style={styles.selectionContainer}>
                        <SelectionButton
                          isSelected={userSelections[disease.id]?.role === 'patient'}
                          onPress={() => handleRoleSelection(disease.id, 'patient')}
                          label="I have this condition"
                          multiSelect={false}
                        />
                        <SelectionButton
                          isSelected={userSelections[disease.id]?.role === 'caregiver'}
                          onPress={() => handleRoleSelection(disease.id, 'caregiver')}
                          label="I'm a caregiver for someone with this condition"
                          multiSelect={false}
                        />
                      </View>

                      {userSelections[disease.id]?.role === 'patient' && (
                        <>
                          <Text style={styles.sectionTitle}>
                            Have you received a diagnosis from a medical professional?
                          </Text>
                          <View style={styles.selectionContainer}>
                            <SelectionButton
                              isSelected={userSelections[disease.id]?.diagnosisStatus === 'diagnosed'}
                              onPress={() => handleDiagnosisStatusSelection(disease.id, 'diagnosed')}
                              label="Yes, I have been diagnosed"
                              multiSelect={false}
                            />
                            <SelectionButton
                              isSelected={userSelections[disease.id]?.diagnosisStatus === 'not-diagnosed'}
                              onPress={() => handleDiagnosisStatusSelection(disease.id, 'not-diagnosed')}
                              label="No, but I suspect I might have it"
                              multiSelect={false}
                            />
                          </View>
                        </>
                      )}
                    </View>
                    
                    {index < diseases.selectedDiseases.length - 1 && (
                      <View style={styles.divider} />
                    )}
                  </View>
                ))}
              </View>
              
              <BackNextButton
                onBackPress={handleBack}
                onNextPress={handleNext}
                nextTitle={hasAnyDiagnosedConditions() ? "Next" : "Complete"}
                nextInactive={!isSelectionComplete()}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </GradientBackground>
  );
};

export default UserType;
