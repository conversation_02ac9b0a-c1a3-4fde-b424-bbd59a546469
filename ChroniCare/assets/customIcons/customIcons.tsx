import * as React from "react"
import Svg, { Rect, Path } from "react-native-svg"

interface CustomIconProps {
  size?: number
  stroke?: string
  fill?: string
  strokeWidth?: number
  [key: string]: any
}

function Poop({
  size = 24,
  stroke = "#171717",
  fill = "transparent",
  strokeWidth = 1.5,
  ...props
}: CustomIconProps) {
  return (
    <Svg
      width={size} 
      height={size}
      viewBox="0 0 16 15"
      fill="none"
      {...props}
    >
      <Rect
        x={0.75}
        y={10.2357}
        width={14.5}
        height={3.83333}
        rx={1.91667}
        stroke={stroke}
        strokeWidth={1.5}
      />
      <Rect
        x={3.1488}
        y={6.80714}
        width={10.6905}
        height={3.37619}
        rx={1.68809}
        stroke={stroke}
        strokeWidth={1.5}
      />
      <Path
        d="M12.151 4.838c0 1.22-1.045 1.22-1.045 1.22H5.36s-.522-.688-.522-1.22.522-1.219.522-1.219 1.045-2.438 1.568-2.438c.522 0 0 1.219 0 1.219s.522 1.22 1.045 1.22h3.134c0-.001 1.045-.001 1.045 1.218z"
        stroke={stroke}
        strokeWidth={1.5}
      />
    </Svg>
  )
}

export default Poop