export interface medicationList {
  id: string;
  genericName: string;
  brandNames: string[];
  localizedNames?: {
    [locale: string]: string;
  };
  atcCode?: string;
  drugClass?: string;
  drugType?: string;
}


export const medications: medicationList[] = [
  {
    id: 'adalimumab',
    genericName: 'Adalimumab',
    brandNames: [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON>yr<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      'YUSIMRY',
      '<PERSON><PERSON>',
      'Simlandi'
    ],
    localizedNames: { da: 'Adalimumab' },
    atcCode: 'L04AB04',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Anti-TNF'
  },
  {
    id: 'infliximab',
    genericName: 'Infliximab',
    brandNames: [
      'Remicade',
      'Inflectra',
      'Avsola',
      'Renflexis',
      'IXIFI',
      'Unbranded Infliximab',
      'Zymfen<PERSON>'
    ],
    localizedNames: { da: 'Infliximab' },
    atcCode: 'L04AB02',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Anti-TNF'
  },
  {
    id: 'ustekinumab',
    genericName: 'Ustekinumab',
    brandNames: [
      'Stelara',
      'Pyzchiva',
      'Selarsdi',
      'Wezlana',
      'Yesintek',
      'Otulfi',
      'Imuldosa'
    ],
    localizedNames: { da: 'Ustekinumab' },
    atcCode: 'L04AC05',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Interleukin-12 and -23 Antagonist'
  },
  {
    id: 'vedolizumab',
    genericName: 'Vedolizumab',
    brandNames: ['Entyvio'],
    localizedNames: { da: 'Vedolizumab' },
    atcCode: 'L04AA33',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Integrin Receptor Antagonist'
  },
  {
    id: 'natalizumab',
    genericName: 'Natalizumab',
    brandNames: ['Tysabri', 'Tyruko'],
    localizedNames: { da: 'Natalizumab' },
    atcCode: 'L04AA23',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Integrin Receptor Antagonist'
  },
  {
    id: 'methotrexate',
    genericName: 'Methotrexate',
    brandNames: [
      'Jylamvo',
      'Otrexup',
      'Rasuvo',
      'Xatmep'
    ],
    localizedNames: { da: 'Methotrexat' },
    atcCode: 'L01BA01',
    drugClass: 'Immunomodulators',
    drugType: ''
  },
  {
    id: 'azathioprine',
    genericName: 'Azathioprine',
    brandNames: ['Imuran'],
    localizedNames: { da: 'Azathioprin' },
    atcCode: 'L04AX01',
    drugClass: 'Immunomodulators',
    drugType: ''
  },
  {
    id: 'mercaptopurine',
    genericName: 'Mercaptopurine',
    brandNames: ['Purinethol'],
    localizedNames: { da: 'Mercaptopurin' },
    atcCode: 'L01BB02',
    drugClass: 'Immunomodulators',
    drugType: ''
  },
  {
    id: 'budesonide',
    genericName: 'Budesonide',
    brandNames: ['Entocort EC'],
    localizedNames: { da: 'Budesonid' },
    atcCode: 'A07EA06',
    drugClass: 'Corticosteroids',
    drugType: ''
  },
  {
    id: 'methylprednisolone',
    genericName: 'Methylprednisolone',
    brandNames: [
      'Depo-Medrol',
      'Solu-Medrol',
      'Medrol Dosepak'
    ],
    localizedNames: { da: 'Methylprednisolon' },
    atcCode: 'H02AB04',
    drugClass: 'Corticosteroids',
    drugType: ''
  },
  {
    id: 'prednisolone',
    genericName: 'Prednisolone',
    brandNames: ['Pediapred'],
    localizedNames: { da: 'Prednisolon' },
    atcCode: 'H02AB06',
    drugClass: 'Corticosteroids',
    drugType: ''
  },
  {
    id: 'prednisone',
    genericName: 'Prednisone',
    brandNames: [],
    localizedNames: { da: 'Prednison' },
    atcCode: 'H02AB07',
    drugClass: 'Corticosteroids',
    drugType: ''
  },
  {
    id: 'upadacitinib',
    genericName: 'Upadacitinib',
    brandNames: ['RINVOQ'],
    localizedNames: { da: 'Upadacitinib' },
    atcCode: 'L04AA44',
    drugClass: 'Targeted Synthetic Small Molecules',
    drugType: 'JAK Inhibitor'
  },
  {
    id: 'mirikizumab',
    genericName: 'Mirikizumab',
    brandNames: ['Omvoh'],
    localizedNames: { da: 'Mirikizumab' },
    atcCode: 'L04AC21',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Interleukin-23 Antagonist'
  },
  {
    id: 'risankizumab',
    genericName: 'Risankizumab',
    brandNames: ['Skyrizi'],
    localizedNames: { da: 'Risankizumab' },
    atcCode: 'L04AC19',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Interleukin-23 Antagonist'
  },
  {
    id: 'guselkumab',
    genericName: 'Guselkumab',
    brandNames: ['Tremfya'],
    localizedNames: { da: 'Guselkumab' },
    atcCode: 'L04AC18',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Interleukin-23 Antagonist'
  },
  {
    id: 'certolizumab',
    genericName: 'Certolizumab pegol',
    brandNames: ['Cimzia'],
    localizedNames: { da: 'Certolizumab pegol' },
    atcCode: 'L04AB05',
    drugClass: 'Biologics/Biosimilars',
    drugType: 'Anti-TNF'
  },
  {
    id: 'ciprofloxacin',
    genericName: 'Ciprofloxacin',
    brandNames: ['Cipro'],
    localizedNames: { da: 'Ciprofloxacin' },
    atcCode: 'J01MA02',
    drugClass: 'Antibiotics',
    drugType: ''
  },
  {
    id: 'metronidazole',
    genericName: 'Metronidazole',
    brandNames: ['Flagyl'],
    localizedNames: { da: 'Metronidazol' },
    atcCode: 'J01XD01',
    drugClass: 'Antibiotics',
    drugType: ''
  },
  {
    id: 'tacrolimus',
    genericName: 'Tacrolimus',
    brandNames: ['Prograf'],
    localizedNames: { da: 'Tacrolimus' },
    atcCode: 'L04AD02',
    drugClass: 'Immunomodulators',
    drugType: ''
  }
];