import { ComboBoxItem } from '../components/forms/ComboBox'; // Adjust path as necessary

// Data for IBD conditions including ICD-11 codes
export const diseasesData: ComboBoxItem[] = [
  {
    id: '1',
    label: "<PERSON><PERSON><PERSON>'s Disease",
    icdCode: 'DD70', // ICD-11 code for <PERSON><PERSON><PERSON>'s Disease
    // Note: ICD-11 allows for more detailed post-coordination if needed.
  },
  {
    id: '2',
    label: 'Ulcerative Colitis',
    icdCode: 'DD71', // ICD-11 code for Ulcerative Colitis
  },
  {
    id: '3',
    label: 'Inflammatory Bowel Disease (IBD)',
    // Using the unspecified IBD code from ICD-11
    icdCode: 'DD7Z', // ICD-11 code for Inflammatory bowel diseases, unspecified
  },
  {
    id: '4',
    label: 'Irritable Bowel Syndrome (IBS)',
    icdCode: 'DD91', // ICD-11 code for Irritable bowel syndrome
    // Note: ICD-11 has subtypes like DD91.00 (constipation predominant), DD91.01 (diarrhoea predominant), etc.
  },
  {
    id: '5',
    label: 'Microscopic Colitis',
    icdCode: 'DB33.1', // ICD-11 code for Microscopic Colitis
  }
];
