export interface MedicalDevice {
    id: string;
    genericName: string;
    brandNames: string[];
    localizedNames?: {
      [locale: string]: string;
    };
    deviceClass?: string; // e.g., "Injection Device", "Stoma Care"
    description?: string;
  }


  export const medicalDevices: MedicalDevice[] = [
    {
      id: 'adalimumab_auto_injector',
      genericName: 'Adalimumab Auto-Injector',
      brandNames: ['Humira Pen', 'Amjevita Auto-injector', 'Hyrimoz Pen'],
      localizedNames: { da: 'Adalimumab autoinjektor' },
      deviceClass: 'Injection Device',
      description: 'Pre-filled pen device for subcutaneous administration of adalimumab.'
    },
    {
      id: 'infliximab_infusion_set',
      genericName: 'Infliximab Infusion Set',
      brandNames: ['Remicade Infusion Kit', 'Inflectra Infusion Set'],
      localizedNames: { da: 'Infliximab infusionssæt' },
      deviceClass: 'Infusion Device',
      description: 'IV administration kit for infliximab infusions.'
    },
    {
      id: 'vedolizumab_infusion_set',
      genericName: 'Vedolizumab Infusion Set',
      brandNames: ['Entyvio Infusion Set'],
      localizedNames: { da: 'Vedolizumab infusionssæt' },
      deviceClass: 'Infusion Device',
      description: 'IV administration kit for vedolizumab infusions.'
    },
    {
      id: 'methotrexate_auto_injector',
      genericName: 'Methotrexate Auto-Injector',
      brandNames: ['Otrexup Auto-Injector', 'Rasuvo Pen'],
      localizedNames: { da: 'Methotrexat autoinjektor' },
      deviceClass: 'Injection Device',
      description: 'Single-dose auto-injector for subcutaneous methotrexate.'
    },
    {
      id: 'stoma_pouching_system',
      genericName: 'Stoma Pouching System',
      brandNames: ['SenSura Mio', 'Hollister New Image'],
      localizedNames: { da: 'Stomipose-system' },
      deviceClass: 'Stoma Care',
      description: 'Ostomy pouch system for ileostomy or colostomy management.'
    },
    {
      id: 'stoma_skin_barrier',
      genericName: 'Stoma Skin Barrier',
      brandNames: ['CeraPlus Skin Barrier', 'Brava Barrier'],
      localizedNames: { da: 'Stomi hudbeskyttelse' },
      deviceClass: 'Stoma Care',
      description: 'Protective barrier applied to skin around the stoma.'
    },
    {
      id: 'rectal_enema_applicator',
      genericName: 'Rectal Enema Applicator',
      brandNames: ['Salofalk Enema Bottle'],
      localizedNames: { da: 'Rektal klyster-applikator' },
      deviceClass: 'Rectal Delivery Device',
      description: 'Applicator for rectal administration of mesalazine enemas.'
    },
    {
      id: 'suppository_applicator',
      genericName: 'Suppository Applicator',
      brandNames: ['Canesten Applicator (adapted)'],
      localizedNames: { da: 'Suppositorie-applikator' },
      deviceClass: 'Rectal Delivery Device',
      description: 'Device to insert suppositories into the rectum.'
    },
    {
      id: 'parenteral_syringe',
      genericName: 'Parenteral Syringe',
      brandNames: ['BD SafetyGlide Syringe'],
      localizedNames: { da: 'Parenteral sprøjte' },
      deviceClass: 'Injection Device',
      description: 'Sterile syringe for subcutaneous or intramuscular injection.'
    }
  ];