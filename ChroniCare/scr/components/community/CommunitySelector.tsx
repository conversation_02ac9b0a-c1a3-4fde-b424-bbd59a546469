import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  Dimensions,
} from "react-native";
import { useTheme } from "../../../scr/context/themeContext";
import { ChevronsUpDown } from "lucide-react-native";

interface Community {
  id: string;
  name: string;
}



interface CommunitySelectorProps {
  communities: Community[];
  selectedCommunity: string | null;
  onSelect: (communityId: string) => void;
  placeholder?: string;
}

const CommunitySelector: React.FC<CommunitySelectorProps> = ({
  communities,
  selectedCommunity,
  onSelect,
  placeholder = "Select a community",
}) => {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0, height: 0 });
  const selectorRef = useRef<View>(null);

  const selectedCommunityName =
    communities.find((c) => c.id === selectedCommunity)?.name || placeholder;
  
  const singleCommunityName = communities.length === 1 ? communities[0].name : null;


  const handlePress = () => {
    if (communities.length <= 1) return;
    selectorRef.current?.measure((_fx: number, _fy: number, width: number, height: number, px: number, py: number) => {
      const top = py;
      const left = px;
      setPosition({ top, left, width, height });
      setIsOpen(true);
    });
  };

  const handleSelect = (communityId: string) => {
    onSelect(communityId);
    setIsOpen(false);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      height: 50,
      backgroundColor: theme.colors.Background.backroundMuted,
      borderRadius: theme.spacing.borderRadius.sm,
      paddingHorizontal: theme.spacing.spacing.s2,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    singleCommunityContainer: {
      backgroundColor: theme.colors.Background.background0,
      justifyContent: 'center'
    },
    text: {
      fontSize: theme.typography.textSize.md,
      color: theme.colors.Text.text900,
      fontFamily: theme.typography.fontFamily.inter,
    },
    placeholder: {
      color: theme.colors.Text.text500,
    },
    modalOverlay: {
      flex: 1,
    },
    optionsContainer: {
      position: "absolute",
      backgroundColor: theme.colors.Background.background0,
      borderRadius: theme.spacing.borderRadius.sm,
      borderWidth: 1,
      borderColor: theme.colors.Background.background200,
      padding: theme.spacing.spacing.s1,
      bottom: 0, 
      left: 0,
      right: 0
    },
    optionItem: {
      paddingVertical: theme.spacing.spacing.s2,
      paddingHorizontal: theme.spacing.spacing.s2,
    },
    optionText: {
      fontSize: theme.typography.textSize.md,
      fontFamily: theme.typography.fontFamily.inter,
      color: theme.colors.Text.text900,
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.Background.background100,
      marginHorizontal: theme.spacing.spacing.s2,
    },
  });

  if (singleCommunityName) {
    return (
        <View style={[styles.container, styles.singleCommunityContainer]}>
            <Text style={styles.text}>{singleCommunityName}</Text>
        </View>
    );
  }

  return (
    <>
      <TouchableOpacity
        ref={selectorRef}
        style={styles.container}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.text,
            !selectedCommunity && styles.placeholder,
          ]}
        >
          {selectedCommunityName}
        </Text>
        <ChevronsUpDown size={16} color={theme.colors.Text.text500} />
      </TouchableOpacity>

      <Modal visible={isOpen} transparent={true} onRequestClose={() => setIsOpen(false)} animationType="fade">
        <TouchableOpacity style={styles.modalOverlay} activeOpacity={1} onPress={() => setIsOpen(false)}>
            <View 
              style={[
                  styles.optionsContainer, 
                  { 
                      width: position.width,
                      left: position.left,
                      bottom: Dimensions.get('window').height - (position.top + position.height),
                  }
              ]}
            >
            <FlatList
                data={communities}
                keyExtractor={(item) => item.id}
                keyboardShouldPersistTaps="always"
                renderItem={({ item }) => (
                <TouchableOpacity
                    onPress={() => handleSelect(item.id)}
                    style={styles.optionItem}
                >
                    <Text style={styles.optionText}>{item.name}</Text>
                </TouchableOpacity>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                ListFooterComponent={
                  <TouchableOpacity
                    onPress={() => setIsOpen(false)}
                    style={styles.optionItem}
                  >
                    <Text style={[styles.text, !selectedCommunity && styles.placeholder]}>{selectedCommunityName}</Text>
                  </TouchableOpacity>
                }
            />
            </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default CommunitySelector; 