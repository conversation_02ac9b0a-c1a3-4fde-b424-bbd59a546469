import { View, Text, StyleSheet, Pressable, Modal, TouchableOpacity, Alert } from 'react-native';
import React, { useMemo, useState, useCallback, useRef } from 'react';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { runOnJS, useSharedValue } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { useThreadReactions } from '@/scr/hooks/useThreadReactions';
import { useCommentReactions } from '@/scr/hooks/useCommentReactions';
import { useReactionDisplay } from '@/scr/hooks/useReactionDisplay';
import ReactionIcon from './ReactionIcon';
import DraggableReactionSelector, {
  REACTION_SIZE,
  REACTION_SPACING,
  SELECTOR_HEIGHT,
} from './DraggableReactionSelector';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

interface ThreadReactionData {
  _id: string;
  reactionCounts: ReactionCount;
  myReaction: string | null;
}

interface CommentReactionData {
  _id: string;
  reactionCounts: ReactionCount;
  myReaction: string | null;
}

interface ThreadReactionsProps {
  type: 'thread';
  thread: ThreadReactionData;
  showBorder?: boolean;
}

interface CommentReactionsProps {
  type: 'comment';
  comment: CommentReactionData;
  showBorder?: boolean;
}

type ReactionsProps = ThreadReactionsProps | CommentReactionsProps;

// Thread-specific reactions component
const ThreadReactions = ({ thread, showBorder }: { thread: ThreadReactionData, showBorder?: boolean }) => {
  const { addReaction, errorMessage } = useThreadReactions(thread._id);

  return (
    <ReactionsUI
      data={thread}
      addReaction={addReaction}
      errorMessage={errorMessage}
      showBorder={showBorder}
    />
  );
};

// Comment-specific reactions component
const CommentReactions = ({ comment, showBorder }: { comment: CommentReactionData, showBorder?: boolean }) => {
  const { addReaction, errorMessage } = useCommentReactions(comment._id);

  return (
    <ReactionsUI
      data={comment}
      addReaction={addReaction}
      errorMessage={errorMessage}
      showBorder={showBorder}
    />
  );
};

// Main dispatcher component
const Reactions = (props: ReactionsProps) => {
  if (props.type === 'thread') {
    return <ThreadReactions thread={props.thread} showBorder={props.showBorder} />;
  } else {
    return <CommentReactions comment={props.comment} showBorder={props.showBorder} />;
  }
};

// Shared UI component
interface ReactionsUIProps {
  data: ThreadReactionData | CommentReactionData | null;
  addReaction: (reaction: ReactionType) => void;
  errorMessage: string | null;
  showBorder?: boolean;
}

const ReactionsUI = ({ data, addReaction, errorMessage, showBorder = true }: ReactionsUIProps) => {
  const { theme } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [draggableSelectorVisible, setDraggableSelectorVisible] = useState(false);
  const [triggerPosition, setTriggerPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const [hoveredReaction, setHoveredReaction] = useState<ReactionType | null>(null);
  const buttonRef = useRef<View>(null);

  // Use the custom hook for reaction display logic
  const { totalReactions, displayedReactions, iconMapping } = useReactionDisplay(data?.reactionCounts || null);

  // Component lifecycle logging
  React.useEffect(() => {
    return () => {
    };
  }, [data?._id]);

  // Show error alert when errorMessage changes
  React.useEffect(() => {
    if (errorMessage) {
      Alert.alert(
        'Reaction Error',
        errorMessage,
        [
          {
            text: 'OK',
            onPress: () => {
              // Error message will be cleared by the hook
            },
          },
        ]
      );
    }
  }, [errorMessage]);

  const myReaction = data?.myReaction as ReactionType | null;

  const handleQuickReaction = useCallback(() => {
    // Add haptic feedback for quick reaction
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // If a reaction is already selected, toggle it. Otherwise, default to 'love'.
    const reactionToToggle = myReaction || 'love';
    addReaction(reactionToToggle);
  }, [addReaction, myReaction]);

  const handleSelectReaction = useCallback((reaction: ReactionType) => {
    // Add haptic feedback for reaction selection
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    addReaction(reaction);
    setModalVisible(false);
  }, [addReaction]);

  const handleLongPress = useCallback(() => {
    // Add haptic feedback for long press
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    // Get the position of the button to position the draggable selector
    buttonRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setTriggerPosition({
        x: pageX,
        y: pageY,
        width: width,
        height: height,
      });
      setDraggableSelectorVisible(true);
    });
  }, []);

  const handleDraggableReactionSelect = useCallback((reaction: ReactionType) => {
    addReaction(reaction);
    setDraggableSelectorVisible(false);
  }, [addReaction]);

  const handleDraggableDismiss = useCallback(() => {
    setDraggableSelectorVisible(false);
    setHoveredReaction(null);
  }, []);

  const calculateSelectorPosition = useCallback(() => {
    const reactions = Object.keys(iconMapping) as ReactionType[];
    const selectorWidth = reactions.length * REACTION_SPACING + 30;
    const centerX = triggerPosition.x + (triggerPosition.width / 2);
    let x = centerX - selectorWidth / 2;
    
    // Keep selector within screen bounds
    if (x < 20) x = 20;
    if (x + selectorWidth > 400 - 20) x = 400 - selectorWidth - 20; // Approximate screen width
    
    const y = triggerPosition.y - SELECTOR_HEIGHT - 10;
    
    return { x, y, width: selectorWidth };
  }, [triggerPosition, iconMapping]);

  const handleDrag = useCallback((gestureX: number, gestureY: number) => {
    if (!draggableSelectorVisible) return;

    const selectorPosition = calculateSelectorPosition();
    const reactions = Object.keys(iconMapping) as ReactionType[];
    const numReactions = reactions.length;

    // The selector container uses `justifyContent: 'space-around'`.
    // To accurately find the hovered reaction, we must calculate the position
    // of each icon based on that layout style.
    const contentWidth = selectorPosition.width;
    const totalReactionsWidth = numReactions * REACTION_SIZE;
    const totalSpacing = contentWidth - totalReactionsWidth;

    // With 'space-around', the space between each item is equal, and the space
    // at the ends is half of that amount.
    const spaceBetweenReactions = totalSpacing / numReactions;
    const spaceAtEnds = spaceBetweenReactions / 2;

    let closestReaction: ReactionType | null = null;
    let minDistance = Infinity;

    // Determine which reaction is closest to the user's finger.
    reactions.forEach((reaction, index) => {
      const reactionCenterX =
        selectorPosition.x +
        spaceAtEnds +
        index * (REACTION_SIZE + spaceBetweenReactions) +
        REACTION_SIZE / 2;
      
      const distance = Math.abs(gestureX - reactionCenterX);

      if (distance < minDistance) {
        minDistance = distance;
        closestReaction = reaction;
      }
    });

    // Ensure the finger is vertically within the selector's bounds.
    const isWithinVerticalBounds =
      gestureY > selectorPosition.y &&
      gestureY < selectorPosition.y + SELECTOR_HEIGHT;

    const finalHoveredReaction = isWithinVerticalBounds ? closestReaction : null;

    if (finalHoveredReaction !== hoveredReaction) {
      if (finalHoveredReaction) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      setHoveredReaction(finalHoveredReaction);
    }
  }, [draggableSelectorVisible, hoveredReaction, calculateSelectorPosition, iconMapping]);

  const handleDragEnd = useCallback(() => {
    if (hoveredReaction) {
      addReaction(hoveredReaction);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    setDraggableSelectorVisible(false);
    setHoveredReaction(null);
  }, [hoveredReaction, addReaction]);

  // Cleanup timer on unmount
  React.useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, []);

  // Use shared values for gesture timing
  const longPressTriggered = useSharedValue(false);
  const longPressTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Helper function to start long press timer
  const startLongPressTimer = useCallback(() => {
    longPressTimer.current = setTimeout(() => {
      if (!longPressTriggered.value) {
        longPressTriggered.value = true;
        runOnJS(handleLongPress)();
      }
    }, 400);
  }, [handleLongPress, longPressTriggered]);

  // Helper function to clear long press timer
  const clearLongPressTimer = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, []);

  // Create gesture for the reaction button
  const reactionButtonGesture = useMemo(() => {
    // This gesture composition robustly handles three distinct interactions:
    // 1. Quick Tap: Fires a reaction immediately.
    // 2. Long Press: Opens the reaction selector.
    // 3. Drag: Selects a reaction after a long press.
    // It is composed with Gesture.Native() to prevent conflicts with parent ScrollViews.

    // Tap gesture handles the quick tap and initiates the long press timer.
    const tapGesture = Gesture.Tap()
      .maxDuration(10000)
      .onBegin(() => {
        'worklet';
        longPressTriggered.value = false;
        runOnJS(startLongPressTimer)();
      })
      .onFinalize((_event, success) => {
        'worklet';
        runOnJS(clearLongPressTimer)();

        // onFinalize is called when the tap gesture completes or is cancelled.
        // We only act if the gesture was successful (i.e., not cancelled by the pan).
        if (success) {
          if (longPressTriggered.value) {
            // Case: Long press without a drag, then finger is lifted.
            // The pan gesture won't activate, so we close the selector here.
            runOnJS(handleDragEnd)();
          } else {
            // Case: A standard quick tap.
            runOnJS(handleQuickReaction)();
          }
        }
        // If `success` is false, the pan gesture has taken over. The pan gesture's
        // `onEnd` will be responsible for calling `handleDragEnd`.
      });

    // Pan gesture handles dragging ONLY after a long press has been detected.
    const panGesture = Gesture.Pan()
      .minDistance(8)
      .onUpdate((event) => {
        'worklet';
        if (longPressTriggered.value) {
          runOnJS(handleDrag)(event.absoluteX, event.absoluteY);
        }
      })
      .onEnd(() => {
        'worklet';
        if (longPressTriggered.value) {
          // This handles closing the selector after a drag has finished.
          runOnJS(handleDragEnd)();
        }
        longPressTriggered.value = false; // Reset for the next interaction.
      });

    const internalGestures = Gesture.Simultaneous(tapGesture, panGesture);

    return Gesture.Race(internalGestures, Gesture.Native());
  }, [
    handleQuickReaction,
    handleDrag,
    handleDragEnd,
    startLongPressTimer,
    clearLongPressTimer,
    longPressTriggered,
  ]);

  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flexDirection: 'row',
          alignItems: 'center',
          borderRadius: 20,
          borderWidth: showBorder ? 1 : 0,
          borderColor: theme.colors.Text.text100,
          paddingVertical: theme.spacing.spacing.s1,
          paddingHorizontal: theme.spacing.spacing.s1,
          alignSelf: 'flex-start',
        },
        iconsContainer: {
          flexDirection: 'row',
          alignItems: 'center',
        },
        overlappingIcon: {
          marginLeft: -8,
        },
        totalText: {
          ...theme.textVariants.text('sm', 'regular'),
          color: theme.colors.Text.text900,
          marginLeft: theme.spacing.spacing.s2,
        },
        modalContainer: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        },
        modalContent: {
          flexDirection: 'row',
          backgroundColor: theme.colors.Background.background100,
          borderRadius: 30,
          padding: theme.spacing.spacing.s4,
          elevation: 5,
          gap: theme.spacing.spacing.s3,
        },
      }),
    [theme, showBorder],
  );

  return (
    <>
      <GestureDetector gesture={reactionButtonGesture}>
        <View
          ref={buttonRef}
          style={styles.container}
          accessibilityRole="button"
          pointerEvents="box-only">
          <View style={styles.iconsContainer}>
            {displayedReactions.map(({ key }, index) => (
              <View
                key={key}
                style={[
                  index > 0 && styles.overlappingIcon,
                  { zIndex: displayedReactions.length - index },
                ]}>
                <ReactionIcon 
                  reactionType={key}
                  isSelected={myReaction === key}
                  showBorder={true}
                />
              </View>
            ))}
          </View>
          <Text style={styles.totalText}>{totalReactions}</Text>
        </View>
      </GestureDetector>
      <DraggableReactionSelector
        visible={draggableSelectorVisible}
        onReactionSelect={handleDraggableReactionSelect}
        onDismiss={handleDraggableDismiss}
        triggerPosition={triggerPosition}
        myReaction={myReaction}
        hoveredReaction={hoveredReaction}
      />
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
        accessibilityViewIsModal={true}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}
          accessibilityRole="button">
          <View style={styles.modalContent}>
            {Object.entries(iconMapping).map(([key]) => (
              <Pressable
                key={key}
                onPress={() => handleSelectReaction(key as ReactionType)}
                accessibilityRole="button"
                style={({ pressed }) => ({
                  opacity: pressed ? 0.7 : 1,
                })}>
                  <ReactionIcon
                    reactionType={key as ReactionType}
                    isSelected={myReaction === key}
                    size={32}
                  />
              </Pressable>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default React.memo(Reactions);