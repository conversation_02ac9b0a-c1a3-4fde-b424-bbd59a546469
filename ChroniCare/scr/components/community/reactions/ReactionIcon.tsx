import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { Heart, HeartHandshake, Laugh, Lightbulb } from 'lucide-react-native';
import Poop from '@/assets/customIcons/customIcons';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

const iconMap: Record<ReactionType, React.ElementType> = {
  love: Heart,
  withYou: HeartHandshake,
  funny: Laugh,
  insightful: Lightbulb,
  poop: Poop,
};

interface ReactionIconProps {
  reactionType: ReactionType;
  isSelected: boolean;
  size?: number;
  strokeWidth?: number;
  showBorder?: boolean;
}

const ReactionIcon = ({
  reactionType,
  isSelected,
  size = 16,
  strokeWidth = 2.5,
  showBorder = false,
}: ReactionIconProps) => {
  const { theme } = useTheme();
  const IconComponent = iconMap[reactionType];

  const iconColors = theme.colors.Reaction;

  const strokeColor = isSelected ? iconColors.stroke[reactionType] : theme.colors.Text.text900;
  const fillColor = isSelected ? iconColors.fill[reactionType] : 'transparent';
  const backgroundColor = isSelected ? iconColors.background[reactionType] : theme.colors.Background.background100;
  
  const styles = StyleSheet.create({
    iconWrapper: {
      width: size * 1.5,
      height: size * 1.5,
      borderRadius: 9999,
      backgroundColor: backgroundColor,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: showBorder ? 2 : 0,
      borderColor: theme.colors.Background.background0,
    },
  });

  return (
    <View style={styles.iconWrapper}>
      <IconComponent
        size={size}
        stroke={strokeColor}
        fill={fillColor}
        strokeWidth={strokeWidth}
      />
    </View>
  );
};

export default React.memo(ReactionIcon); 