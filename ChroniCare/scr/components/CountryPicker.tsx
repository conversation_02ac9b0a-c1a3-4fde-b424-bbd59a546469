import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import { ChevronDown, ChevronUp, Search, X } from 'lucide-react-native';
import { useTheme } from '../context/themeContext';
import countries from 'i18n-iso-countries';
import enJson from 'i18n-iso-countries/langs/en.json';

interface CountryOption {
  code: string;
  name: string;
  englishName: string;
}

interface CountryPickerProps {
  label: string;
  placeholder: string;
  selectedValue?: string;
  onSelect: (countryCode: string, countryName: string) => void;
  required?: boolean;
  error?: string;
  localLanguageCode: string;
}

export const CountryPicker: React.FC<CountryPickerProps> = ({
  label,
  placeholder,
  selectedValue,
  onSelect,
  required = false,
  error,
  localLanguageCode,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLocalLanguageLoaded, setIsLocalLanguageLoaded] = useState(false);
  const { theme } = useTheme();
  
  // Bottom Sheet Modal ref
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  
  // Search input ref for auto-focus
  const searchInputRef = useRef<TextInput>(null);
  
  // Bottom Sheet snap points
  const snapPoints = useMemo(() => ['60%', '90%'], []);

  // Register English locale immediately
  countries.registerLocale(enJson);
  
  // Load local language asynchronously
  useEffect(() => {
    const loadLocalLanguage = async () => {
      if (localLanguageCode !== 'en') {
        switch (localLanguageCode) {
          case 'da': {
            const localeModule = await import(
              /* webpackChunkName: "country-da" */ 'i18n-iso-countries/langs/da.json'
            );
            countries.registerLocale(localeModule.default || localeModule);
            break;
          }
          case 'sv': {
            const localeModule = await import(
              /* webpackChunkName: "country-sv" */ 'i18n-iso-countries/langs/sv.json'
            );
            countries.registerLocale(localeModule.default || localeModule);
            break;
          }
          case 'no': {
            const localeModule = await import(
              /* webpackChunkName: "country-no" */ 'i18n-iso-countries/langs/no.json'
            );
            countries.registerLocale(localeModule.default || localeModule);
            break;
          }
          case 'fi': {
            const localeModule = await import(
              /* webpackChunkName: "country-fi" */ 'i18n-iso-countries/langs/fi.json'
            );
            countries.registerLocale(localeModule.default || localeModule);
            break;
          }
          default:
            console.warn(`Language ${localLanguageCode} not supported by i18n-iso-countries`);
            break;
        }
      }
      // Indicate that we're done loading (or falling back to English)
      setIsLocalLanguageLoaded(true);
    };

    loadLocalLanguage();
  }, [localLanguageCode]);

  // Get all countries with names in both languages
  const countryOptions = useMemo(() => {
    // Don't generate country list until local language is loaded (or determined not to load)
    if (!isLocalLanguageLoaded) {
      return [];
    }

    try {
      const allCountries = countries.getAlpha2Codes();
      const options: CountryOption[] = Object.keys(allCountries).map((code) => {
        const englishName = countries.getName(code, 'en') || '';
        
        // Try to get local name if different from English and language is loaded
        let localName = englishName;
        if (localLanguageCode !== 'en') {
          try {
            const possibleLocalName = countries.getName(code, localLanguageCode);
            if (possibleLocalName) {
              localName = possibleLocalName;
            }
          } catch (error) {
            // Fallback to English name if local name fails
            localName = englishName;
          }
        }
        
        return {
          code,
          name: localName,
          englishName,
        };
      }).filter(option => option.englishName) // Filter out countries without names
        .sort((a, b) => a.name.localeCompare(b.name));
      
      return options;
    } catch (error) {
      console.error('Error loading countries:', error);
      return [];
    }
  }, [localLanguageCode, isLocalLanguageLoaded]);

  // Filter countries based on search query
  const filteredCountries = useMemo(() => {
    if (!searchQuery.trim()) return countryOptions;
    
    const query = searchQuery.toLowerCase().trim();
    return countryOptions.filter(country => 
      country.name.toLowerCase().includes(query) ||
      country.englishName.toLowerCase().includes(query) ||
      country.code.toLowerCase().includes(query)
    );
  }, [countryOptions, searchQuery]);

  // Get selected country name for display
  const selectedCountryName = useMemo(() => {
    if (!selectedValue) return null;
    const selectedCountry = countryOptions.find(country => country.code === selectedValue);
    return selectedCountry?.name || null;
  }, [selectedValue, countryOptions]);

  const handleSelect = useCallback((country: CountryOption) => {
    onSelect(country.code, country.name);
    bottomSheetModalRef.current?.dismiss();
    setSearchQuery('');
  }, [onSelect]);

  const openModal = useCallback(() => {
    bottomSheetModalRef.current?.present();
    // Auto-focus search input after a small delay to ensure modal is fully opened
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 300);
  }, []);

  const closeModal = useCallback(() => {
    bottomSheetModalRef.current?.dismiss();
    setSearchQuery('');
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);

  // Bottom Sheet Backdrop component
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      width: '100%',
    },
    label: {
      ...theme.textVariants.text('md', 'medium'),
      color: theme.colors.Text.text900,
    },
    required: {
      color: theme.colors.Indicator.error,
    },
    dropdownButton: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: theme.spacing.spacing.s2,
      paddingRight: theme.spacing.spacing.s2,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text900,
      backgroundColor: 'transparent',
    },
    dropdownText: {
      ...theme.textVariants.text('md', 'regular'),
      color: selectedValue ? theme.colors.Text.text900 : theme.colors.Text.text500,
    },
    chevronIcon: {
      marginLeft: 8,
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: theme.typography.textSize.sm,
      marginTop: theme.spacing.spacing.s1,
    },
    bottomSheetContainer: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingVertical: theme.spacing.spacing.s3,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Background.background200,
    },
    modalTitle: {
      ...theme.textVariants.text('xl', 'semibold'),
      color: theme.colors.Text.text900,
    },
    closeButton: {
      padding: theme.spacing.spacing.s1,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: theme.spacing.spacing.s4,
      marginVertical: theme.spacing.spacing.s3,
      paddingHorizontal: theme.spacing.spacing.s3,
      paddingVertical: theme.spacing.spacing.s2,
      backgroundColor: theme.colors.Background.background100,
      borderRadius: theme.spacing.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.Background.background300,
    },
    searchIcon: {
      marginRight: theme.spacing.spacing.s2,
    },
    searchInput: {
      flex: 1,
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text950,
      paddingVertical: theme.spacing.spacing.s1,
    },
    clearButton: {
      padding: theme.spacing.spacing.s1,
      marginLeft: theme.spacing.spacing.s2,
    },
    listContainer: {
      flex: 1,
    },
    countryItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingVertical: theme.spacing.spacing.s3,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Background.background200,
    },
    selectedCountryItem: {
      backgroundColor: theme.colors.Background.background100,
    },
    countryInfo: {
      flex: 1,
    },
    countryName: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text950,
    },
    countryEnglishName: {
      ...theme.textVariants.text('xs', 'regular'),
      color: theme.colors.Text.text600,
      marginTop: 2,
    },
    countryCode: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text600,
      marginLeft: theme.spacing.spacing.s2,
    },
    selectedCountryText: {
      color: theme.colors.Text.text950,
      fontWeight: '600',
    },
    selectedCountrySubtext: {
      color: theme.colors.Text.text700,
    },
    noResults: {
      padding: theme.spacing.spacing.s4,
      alignItems: 'center',
    },
    noResultsText: {
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text600,
      textAlign: 'center',
    },
    loadingContainer: {
      padding: theme.spacing.spacing.s4,
      alignItems: 'center',
    },
  }), [theme, selectedValue]);

  const renderCountryItem = useCallback(({ item }: { item: CountryOption }) => (
    <TouchableOpacity
      style={[
        styles.countryItem,
        selectedValue === item.code && styles.selectedCountryItem,
      ]}
      onPress={() => handleSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.countryInfo}>
        <Text style={[
          styles.countryName,
          selectedValue === item.code && styles.selectedCountryText,
        ]}>
          {item.name}
        </Text>
        {item.name !== item.englishName && (
          <Text style={[
            styles.countryEnglishName,
            selectedValue === item.code && styles.selectedCountrySubtext,
          ]}>
            {item.englishName}
          </Text>
        )}
      </View>
      <Text style={[
        styles.countryCode,
        selectedValue === item.code && styles.selectedCountryText,
      ]}>
        {item.code}
      </Text>
    </TouchableOpacity>
  ), [selectedValue, handleSelect, styles]);

  const keyExtractor = useCallback((item: CountryOption) => item.code, []);

  const renderEmptyComponent = useCallback(() => (
    <View style={styles.noResults}>
      <Text style={styles.noResultsText}>
        {!isLocalLanguageLoaded ? 'Loading countries...' :
         searchQuery.trim() ? 'No countries found matching your search' : 'No countries available'}
      </Text>
    </View>
  ), [searchQuery, styles, isLocalLanguageLoaded]);

  const renderLoadingComponent = useCallback(() => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={theme.colors.Primary.primary500} />
      <Text style={styles.noResultsText}>Loading countries...</Text>
    </View>
  ), [styles, theme]);

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label}
        {required && <Text style={styles.required}>*</Text>}
      </Text>
      
      <TouchableOpacity 
        style={styles.dropdownButton}
        onPress={openModal}
        activeOpacity={0.7}
      >
        <Text style={styles.dropdownText}>
          {selectedCountryName || placeholder}
        </Text>
        <ChevronDown 
          size={20} 
          color={theme.colors.Text.text600}
          style={styles.chevronIcon}
        />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={true}
        backgroundStyle={{
          backgroundColor: theme.colors.Background.background0,
        }}
        handleIndicatorStyle={{
          backgroundColor: theme.colors.Background.background900,
        }}
      >
        <BottomSheetView style={styles.bottomSheetContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Country</Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={closeModal}
              activeOpacity={0.7}
            >
              <X size={24} color={theme.colors.Text.text950} />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <Search 
              size={20} 
              color={theme.colors.Text.text600}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search by country name or code"
              placeholderTextColor={theme.colors.Text.text500}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              autoCorrect={false}
              ref={searchInputRef}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={clearSearch}
                activeOpacity={0.7}
              >
                <X size={16} color={theme.colors.Text.text600} />
              </TouchableOpacity>
            )}
          </View>

          {!isLocalLanguageLoaded ? (
            renderLoadingComponent()
          ) : (
            <FlatList
              style={styles.listContainer}
              data={filteredCountries}
              renderItem={renderCountryItem}
              keyExtractor={keyExtractor}
              ListEmptyComponent={renderEmptyComponent}
              showsVerticalScrollIndicator={true}
              keyboardShouldPersistTaps="handled"
            />
          )}
        </BottomSheetView>
      </BottomSheetModal>
    </View>
  );
}; 