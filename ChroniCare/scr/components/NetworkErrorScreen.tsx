import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '../context/themeContext';

interface NetworkErrorScreenProps {
  onRetry: () => void;
  isRetrying?: boolean;
}

export const NetworkErrorScreen: React.FC<NetworkErrorScreenProps> = ({ 
  onRetry, 
  isRetrying = false 
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.Background.background0 }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.Text.text950 }]}>
          Connection Problem
        </Text>
        
        <Text style={[styles.message, { color: theme.colors.Text.text600 }]}>
          We're having trouble connecting to our servers. Please check your internet connection and try again.
        </Text>

        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.colors.Primary.primary500 }]}
          onPress={onRetry}
          disabled={isRetrying}
        >
          {isRetrying ? (
            <ActivityIndicator size="small" color={theme.colors.Background.background0} />
          ) : (
            <Text style={[styles.retryButtonText, { color: theme.colors.Background.background0 }]}>
              Try Again
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  retryButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
}); 