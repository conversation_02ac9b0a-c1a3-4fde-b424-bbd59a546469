import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import { useTheme } from '../context/themeContext';

interface IconTitleInstructionsProps {
  icon: LucideIcon;
  title: React.ReactNode;
  instructions?: string;
  iconSize?: number;
}

const IconTitleInstructions: React.FC<IconTitleInstructionsProps> = ({
  icon: Icon,
  title,
  instructions,
  iconSize = 32,
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <Icon 
        size={iconSize} 
        color={theme.colors.Text.text950}
        style={styles.icon}
      />

      <Text style={[styles.title, {
        ...theme.textVariants.heading('xl', 'semibold'),
        color: theme.colors.Text.text950,
      }]}>
        {title}
      </Text>

      {instructions && (
        <Text style={[theme.textVariants.text('md', 'regular'), styles.instructions, { color: theme.colors.Text.text900 }]}>
          {instructions}
        </Text>
      )}
    </View>
  );
};

export default IconTitleInstructions;

const styles = StyleSheet.create({
  container: {
    gap: 12,
  },
  icon: {
    alignSelf: 'flex-start',
  },
  title: {
    alignSelf: 'flex-start',
  },
  instructions: {
    alignSelf: 'flex-start',
  },
}); 