import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import { useTheme } from '../../context/themeContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export interface OnboardingSlide {
  id: string;
  image: any;
  title: string;
  description: string;
}

interface OnboardingCarouselProps {
  slides: OnboardingSlide[];
  onSlideChange?: (index: number) => void;
}

const OnboardingCarousel: React.FC<OnboardingCarouselProps> = ({
  slides,
  onSlideChange,
}) => {
  const { theme } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const translateX = useSharedValue(0);

  // Initialize the carousel position
  useEffect(() => {

    // Ensure we start at the first slide
    translateX.value = 0;
  }, [slides.length]);

  const updateIndex = (index: number) => {
    setCurrentIndex(index);
    onSlideChange?.(index);
  };

  const panGesture = Gesture.Pan()
    .onStart(() => {
      // Store the starting position
    })
    .onUpdate((event) => {
      const newTranslateX = -SCREEN_WIDTH * currentIndex + event.translationX;
      // Constrain the translation to not go beyond bounds
      const maxTranslateX = 0;
      const minTranslateX = -SCREEN_WIDTH * (slides.length - 1);
      translateX.value = Math.max(minTranslateX, Math.min(maxTranslateX, newTranslateX));
    })
    .onEnd((event) => {
      const shouldGoToNext = event.translationX < -SCREEN_WIDTH / 6 && currentIndex < slides.length - 1;
      const shouldGoToPrev = event.translationX > SCREEN_WIDTH / 6 && currentIndex > 0;

      if (shouldGoToNext) {
        const nextIndex = Math.min(currentIndex + 1, slides.length - 1);
        translateX.value = withTiming(-SCREEN_WIDTH * nextIndex, { duration: 300 });
        runOnJS(updateIndex)(nextIndex);
      } else if (shouldGoToPrev) {
        const prevIndex = Math.max(currentIndex - 1, 0);
        translateX.value = withTiming(-SCREEN_WIDTH * prevIndex, { duration: 300 });
        runOnJS(updateIndex)(prevIndex);
      } else {
        translateX.value = withTiming(-SCREEN_WIDTH * currentIndex, { duration: 300 });
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
      width: SCREEN_WIDTH * slides.length,
    };
  });

  const createSlideAnimatedStyle = (index: number) => {
    return useAnimatedStyle(() => {
      const inputRange = [
        -SCREEN_WIDTH * (index + 1),
        -SCREEN_WIDTH * index,
        -SCREEN_WIDTH * (index - 1),
      ];
      
      const opacity = interpolate(
        translateX.value,
        inputRange,
        [0.3, 1, 0.3],
        Extrapolation.CLAMP
      );

      const scale = interpolate(
        translateX.value,
        inputRange,
        [0.8, 1, 0.8],
        Extrapolation.CLAMP
      );

      return {
        opacity,
        transform: [{ scale }],
      };
    });
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => {
    const slideAnimatedStyle = createSlideAnimatedStyle(index);
    
    return (
      <Animated.View key={slide.id} style={[styles.slide, slideAnimatedStyle]}>
        <View style={styles.slideContent}>
          <Image
            source={slide.image}
            style={styles.mockupImage}
            resizeMode="contain"
          />
          <Text style={[
            theme.textVariants.heading('xl', 'semibold'), 
            styles.heading,
            { color: theme.colors.Text.text950 }
          ]}>
            {slide.title}
          </Text>
          <Text style={[
            theme.textVariants.text('md', 'regular'),
            styles.description,
            { color: theme.colors.Text.text700 }
          ]}>
            {slide.description}
          </Text>
        </View>
      </Animated.View>
    );
  };

  const renderPaginationDots = () => {
    return (
      <View style={styles.paginationContainer}>
        {slides.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor: index === currentIndex
                  ? theme.colors.Text.text950  // Black for active
                  : theme.colors.Text.text400,  // Gray for inactive
              },
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.carouselSection}>
        <GestureDetector gesture={panGesture}>
          <Animated.View style={[styles.slidesContainer, animatedStyle]}>
            {slides.map((slide, index) => renderSlide(slide, index))}
          </Animated.View>
        </GestureDetector>
      </View>
      {renderPaginationDots()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  carouselSection: {
    flex: 1,
    width: '100%',
    overflow: 'hidden',
  },
  slidesContainer: {
    flexDirection: 'row',
    height: '100%',
  },
  slide: {
    width: SCREEN_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  slideContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 40,
  },
  mockupImage: {
    width: 300,
    height: 400,
    marginBottom: 40,
  },
  heading: {
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    textAlign: 'center',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 24,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});

export default OnboardingCarousel;
