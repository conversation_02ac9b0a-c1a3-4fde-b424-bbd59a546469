import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Check } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../context/themeContext';

interface ConsentSectionProps {
  title: string;
  description?: string;
  onAgreeChange: (isAgreed: boolean) => void;
  isChecked: boolean;
  isRequired?: boolean;
}

const ConsentSection: React.FC<ConsentSectionProps> = ({
  title,
  description,
  onAgreeChange,
  isChecked,
  isRequired,
}) => {
  const { theme } = useTheme();

  const handlePress = () => {
    onAgreeChange(!isChecked);
  };

  const styles = useMemo(
    () =>
      StyleSheet.create({
        gradientWrapper: {
          borderRadius: theme.spacing.borderRadius.xl2,
          padding: theme.spacing.borderWidth.s1,
          shadowColor: theme.colors.Text.text900,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 4,
        },
        container: {
          backgroundColor: theme.colors.Background.background0,
          borderRadius: theme.spacing.borderRadius.xl2,
          padding: theme.spacing.spacing.s3,
          overflow: 'hidden',
        },
        touchableRow: {
          flexDirection: 'row',
          alignItems: 'flex-start',
          gap: theme.spacing.spacing.s3,
        },
        checkboxContainer: {
          width: 20,
          height: 20,
          borderRadius: 4,
          borderWidth: 1,
          borderColor: isChecked
            ? theme.colors.Primary.primary500
            : theme.colors.Text.text300,
          backgroundColor: isChecked
            ? theme.colors.Primary.primary500
            : 'transparent',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 2,
        },
        contentContainer: {
          flex: 1,
          gap: theme.spacing.spacing.s1,
        },
        title: {
          ...theme.textVariants.text('md', 'medium'),
          color: theme.colors.Text.text800
            ,
          lineHeight: 18,
        },
        requiredStar: {
          color: theme.colors.Indicator.error,
        },
        description: {
          ...theme.textVariants.text('sm', 'regular'),
          color: theme.colors.Text.text900,
          lineHeight: 18,
        },
      }),
    [theme, isChecked]
  );

  return (
    <LinearGradient
      colors={[
        theme.colors.Primary.primary100,
        theme.colors.Secondary.secondary50,
      ]}
      start={{ x: 0.5, y: 0 }}
      end={{ x: 0.5, y: 1 }}
      style={styles.gradientWrapper}
    >
      <TouchableOpacity onPress={handlePress} style={styles.container} activeOpacity={0.7}>
        <View style={styles.touchableRow}>
          <View style={styles.checkboxContainer}>
            {isChecked && (
              <Check size={16} color={theme.colors.Text.text0} />
            )}
          </View>
          <View style={styles.contentContainer}>
            <Text style={styles.title}>
              {title}
              {isRequired && <Text style={styles.requiredStar}> *</Text>}
            </Text>
            {description && (
              <Text style={styles.description}>{description}</Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </LinearGradient>
  );
};

export default ConsentSection; 