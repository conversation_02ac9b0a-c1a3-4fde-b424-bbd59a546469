import React from 'react';
import { View, Text, TouchableOpacity, Alert, FlexAlignType, Linking } from 'react-native';
import { useNotifications } from '../../hooks/useNotifications';
import { useSavePushToken } from '../../hooks/useSavePushToken';
import { useTheme } from '../../context/themeContext';
import { notificationPromptUtils } from '../../utils/notificationPromptUtils';

/**
 * Debug component for testing push token functionality
 * This component can be temporarily added to any screen to test push token storage
 */
export const PushTokenDebug: React.FC = () => {
  const theme = useTheme();
  const {
    expoPushToken,
    hasToken,
    isTokenSaving,
    hasTokenSaveError,
    tokenSaveError,
    hasNotificationError,
    error,
    requestPermissions,
    isRequestingPermissions
  } = useNotifications();
  
  const { savePushToken, removePushToken, loading } = useSavePushToken();

  const handleManualSave = async () => {
    if (!expoPushToken) {
      Alert.alert('No Token', 'No push token available to save');
      return;
    }

    try {
      const result = await savePushToken(expoPushToken);
      Alert.alert('Success', `Token saved: ${result}`);
    } catch (error) {
      Alert.alert('Error', `Failed to save token: ${error}`);
    }
  };

  const handleManualRemove = async () => {
    if (!expoPushToken) {
      Alert.alert('No Token', 'No push token available to remove');
      return;
    }

    try {
      const result = await removePushToken(expoPushToken);
      Alert.alert('Success', `Token removed: ${result}`);
    } catch (error) {
      Alert.alert('Error', `Failed to remove token: ${error}`);
    }
  };

  const handleResetPrompt = async () => {
    try {
      await notificationPromptUtils.resetPrompt();
      Alert.alert('Success', 'Notification prompt has been reset. Restart the app to see the prompt again.');
    } catch (error) {
      Alert.alert('Error', `Failed to reset prompt: ${error}`);
    }
  };

  const handleOpenSettings = () => {
    Linking.openSettings();
  };

  const styles = {
    container: {
      padding: theme.theme.spacing.spacing.s4,
      backgroundColor: theme.theme.colors.Background.background0,
      margin: theme.theme.spacing.spacing.s4,
      borderRadius: theme.theme.spacing.spacing.s4,
      borderWidth: 1,
      borderColor: theme.theme.colors.Background.background900,
    },
    title: {
      fontSize: theme.theme.typography.textSize.xl,
      fontWeight: 'bold' as const,
      color: theme.theme.colors.Text.text900,
      marginBottom: theme.theme.spacing.spacing.s4,
    },
    row: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as FlexAlignType,
      marginBottom: theme.theme.spacing.spacing.s4,
      gap: theme.theme.spacing.spacing.s4,
    },
    label: {
      fontSize: theme.theme.typography.textSize.sm,
      color: theme.theme.colors.Text.text900,
      flex: 1,
    },
    value: {
      fontSize: theme.theme.typography.textSize.sm,
      color: theme.theme.colors.Text.text900,
      flex: 2,
      textAlign: 'right' as const,
    },
    token: {
      fontSize: theme.theme.typography.textSize.xs,
      color: theme.theme.colors.Text.text900,
      fontFamily: 'monospace',
      marginVertical: theme.theme.spacing.spacing.s4,
    },
    button: {
      backgroundColor: theme.theme.colors.Primary.primary500,
      padding: theme.theme.spacing.spacing.s4,
      borderRadius: theme.theme.spacing.spacing.s4,
      marginVertical: theme.theme.spacing.spacing.s4,
      alignItems: 'center' as const,
    },
    buttonDisabled: {
      backgroundColor: theme.theme.colors.Background.background0,
    },
    buttonText: {
      color: theme.theme.colors.Text.text900,
      fontSize: theme.theme.typography.textSize.sm,
      fontWeight: '600' as const,
    },
    buttonTextDisabled: {
      color: theme.theme.colors.Text.text900,
    },
    error: {
      color: theme.theme.colors.Secondary.secondary500,
      fontSize: theme.theme.typography.textSize.xs,
      marginTop: theme.theme.spacing.spacing.s4,
    },
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Push Token Debug</Text>
      
      <View style={styles.row}>
        <Text style={styles.label}>Has Token:</Text>
        <Text style={styles.value}>{hasToken ? 'Yes' : 'No'}</Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Token Saving:</Text>
        <Text style={styles.value}>{isTokenSaving ? 'Yes' : 'No'}</Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Save Error:</Text>
        <Text style={styles.value}>{hasTokenSaveError ? 'Yes' : 'No'}</Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Notification Error:</Text>
        <Text style={styles.value}>{hasNotificationError ? 'Yes' : 'No'}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Requesting Permissions:</Text>
        <Text style={styles.value}>{isRequestingPermissions ? 'Yes' : 'No'}</Text>
      </View>

      {expoPushToken && (
        <Text style={styles.token} numberOfLines={2}>
          Token: {expoPushToken}
        </Text>
      )}

      {(tokenSaveError || error) && (
        <Text style={styles.error}>
          Error: {tokenSaveError?.message || error?.message}
        </Text>
      )}

      <TouchableOpacity
        style={[styles.button, (!expoPushToken || loading) && styles.buttonDisabled]}
        onPress={handleManualSave}
        disabled={!expoPushToken || loading}
      >
        <Text style={[styles.buttonText, (!expoPushToken || loading) && styles.buttonTextDisabled]}>
          {loading ? 'Saving...' : 'Manual Save Token'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, (!expoPushToken || loading) && styles.buttonDisabled]}
        onPress={handleManualRemove}
        disabled={!expoPushToken || loading}
      >
        <Text style={[styles.buttonText, (!expoPushToken || loading) && styles.buttonTextDisabled]}>
          {loading ? 'Removing...' : 'Manual Remove Token'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, isRequestingPermissions && styles.buttonDisabled]}
        onPress={requestPermissions}
        disabled={isRequestingPermissions}
      >
        <Text style={[styles.buttonText, isRequestingPermissions && styles.buttonTextDisabled]}>
          {isRequestingPermissions ? 'Requesting...' : 'Request Permissions'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, { backgroundColor: theme.theme.colors.Secondary.secondary500 }]}
        onPress={handleResetPrompt}
      >
        <Text style={styles.buttonText}>
          Reset Notification Prompt
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: theme.theme.colors.Primary.primary500 }]}
        onPress={handleOpenSettings}
      >
        <Text style={styles.buttonText}>
          Open App Settings
        </Text>
      </TouchableOpacity>
    </View>
  );
};
