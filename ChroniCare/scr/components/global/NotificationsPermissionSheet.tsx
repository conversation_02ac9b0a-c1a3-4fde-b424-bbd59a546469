import React, { useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/themeContext';
import { Bell, X } from 'lucide-react-native';

interface NotificationsPermissionSheetProps {
  isVisible: boolean;
  onActivate: () => void;
  onDismiss: () => void;
}

export const NotificationsPermissionSheet: React.FC<NotificationsPermissionSheetProps> = ({
  isVisible,
  onActivate,
  onDismiss,
}) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef<BottomSheet>(null);

  // Snap points for the bottom sheet
  const snapPoints = useMemo(() => ['40%'], []);

  // Handle sheet changes
  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      // Sheet was closed
      onDismiss();
    }
  }, [onDismiss]);

  // Open/close sheet based on isVisible prop
  React.useEffect(() => {
    if (isVisible) {
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [isVisible]);

  const handleActivate = () => {
    onActivate();
    bottomSheetRef.current?.close();
  };

  const handleNotNow = () => {
    onDismiss();
    bottomSheetRef.current?.close();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 24,
      backgroundColor: theme.theme.colors.Background.background0,
    },
    header: {
      alignItems: 'center',
      marginBottom: 24,
    },
    iconContainer: {
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: theme.theme.colors.Primary.primary900,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: theme.theme.spacing.spacing.s4,
    },
    title: {
      fontSize: theme.theme.typography.textSize.xl,
      fontWeight: 'bold',
      color: theme.theme.colors.Text.text900,
      textAlign: 'center',
      marginBottom: theme.theme.spacing.spacing.s4,
    },
    subtitle: {
      fontSize: theme.theme.typography.textSize.md,
      color: theme.theme.colors.Text.text900,
      textAlign: 'center',
    },
    content: {
      marginBottom: theme.theme.spacing.spacing.s4,
    },
    description: {
      fontSize: theme.theme.typography.textSize.sm,
      color: theme.theme.colors.Text.text900,
      textAlign: 'center',
    },
    buttonContainer: {
      gap: 12,
    },
    activateButton: {
      backgroundColor: theme.theme.colors.Primary.primary500,
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center',
    },
    activateButtonText: {
      color: theme.theme.colors.Text.text0,
      fontSize: theme.theme.typography.textSize.md,
      fontWeight: '600',
    },
    dismissButton: {
      backgroundColor: 'transparent',
      paddingVertical: 16,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.theme.colors.Background.background0,
    },
    dismissButtonText: {
      color: theme.theme.colors.Text.text0,
      fontSize: theme.theme.typography.textSize.md,
      fontWeight: '500',
    },
    closeButton: {
      position: 'absolute',
      top: theme.theme.spacing.spacing.s4,
      right: theme.theme.spacing.spacing.s4,
      width: theme.theme.typography.textSize.xl,
      height: theme.theme.typography.textSize.xl,
      borderRadius: 16,
      backgroundColor: theme.theme.colors.Background.background0,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
  
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose={true}
      backgroundStyle={{
        backgroundColor: theme.theme.colors.Background.background0,
      }}
      handleIndicatorStyle={{
        backgroundColor: theme.theme.colors.Background.background900,
      }}
    >
      <BottomSheetView style={styles.container}>
        <TouchableOpacity style={styles.closeButton} onPress={handleNotNow}>
          <X size={16} color={theme.theme.colors.Text.text0} />
        </TouchableOpacity>

        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Bell size={32} color={theme.theme.colors.Text.text0} />
          </View>
          <Text style={styles.title}>Stay Connected</Text>
          <Text style={styles.subtitle}>
            Get notified about important updates and community activity
          </Text>
        </View>

        <View style={styles.content}>
          <Text style={styles.description}>
            We'll send you notifications about new replies to your posts, important community updates, and health reminders. You can change these settings anytime.
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.activateButton} onPress={handleActivate}>
            <Text style={styles.activateButtonText}>Activate Notifications</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.dismissButton} onPress={handleNotNow}>
            <Text style={styles.dismissButtonText}>Not Now</Text>
          </TouchableOpacity>
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
};
