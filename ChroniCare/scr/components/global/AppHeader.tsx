import { View, Image, StyleSheet, TouchableOpacity } from 'react-native';
import React, { useMemo, useState } from 'react';
import { useTheme } from '../../context/themeContext';
import { useUser } from '../../context/userContext'; // Assuming user context provides profile picture URL
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileMenu from './ProfileMenu';
import AvatarPlaceholder from './AvatarPlaceholder';

const AppHeader = () => {
  const theme = useTheme();
  const { user } = useUser();
  const [isMenuVisible, setMenuVisible] = useState(false);

  const logoSource = theme.isDark
    ? require('../../../assets/images/chronicareTextLogoWhite.png')
    : require('../../../assets/images/chronicareText.png');

  const styles = useMemo(() => StyleSheet.create({
    safeArea: {
      justifyContent: 'center',
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.theme.spacing.spacing.s4,
      height: 60,
    },
    logo: {
      width: 120,
      height: 24,
      resizeMode: 'contain',
    },
    profileImage: {
      width: 32,
      height: 32,
      borderRadius: 16,
    },
  }), [theme]);

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.theme.colors.Background.background0 }]} edges={['top']}>
      <View style={styles.container}>
        <Image
          source={logoSource}
          style={styles.logo}
        />
        <TouchableOpacity
          onPress={() => setMenuVisible(true)}
        >
          {user?.photoURL ? (
            <Image
              source={{ uri: user.photoURL }}
              style={styles.profileImage}
            />
          ) : (
            <AvatarPlaceholder
              name={user?.displayName || undefined}
              size={32}
            />
          )}
        </TouchableOpacity>
      </View>
      <ProfileMenu isVisible={isMenuVisible} onClose={() => setMenuVisible(false)} />
    </SafeAreaView>
  );
};

export default AppHeader;