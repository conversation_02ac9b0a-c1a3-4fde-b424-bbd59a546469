import React, { useState, useMemo } from 'react';
import { View, Text, Modal, StyleSheet, TouchableOpacity, TextInput, Platform, ScrollView, SafeAreaView } from 'react-native';
import { useTheme } from '../../context/themeContext';
import { X, Check } from 'lucide-react-native';
import Label from '../forms/Label';
import Button from '../button';
import { processContent } from '../../utils/processContent';
import { useFeedback } from '../../hooks/useFeedback';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';


interface FeedbackModalProps {
  visible: boolean;
  onClose: () => void;
}

const feedbackCategories = [
    "Something isn't working", 
    "Improvement", 
    "Idea/Feature Request", 
    "Compliment",
    "Other"
];



const FeedbackModal: React.FC<FeedbackModalProps> = ({ visible, onClose }) => {
  const { theme } = useTheme();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [feedbackText, setFeedbackText] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const { submitFeedback, loading } = useFeedback();

  const resetFormAndClose = () => {
    onClose();
    setTimeout(() => {
        setSelectedCategories([]);
        setFeedbackText('');
        setIsAnonymous(false);
    }, 300); // Reset after modal close animation
  };

  const handleCategorySelect = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category) 
        : [...prev, category]
    );
  };

  const handleSubmit = async () => {
    try {
        const processedFeedbackText = processContent(feedbackText);
        
      const success = await submitFeedback(selectedCategories, processedFeedbackText, isAnonymous);
      if (success) {
        resetFormAndClose();
      }
    } catch (e) {
      console.error("Failed to submit feedback", e);
      // Here you could show an error message to the user
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    keyboardAvoidingView: {
        flex: 1,
    },
    centeredView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalView: {
        margin: 20,
        backgroundColor: theme.colors.Background.background0,
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
        width: '90%',
    },
    closeButton: {
        alignSelf: 'flex-end',
    },
    modalTitle: {
        ...theme.textVariants.heading('md', 'bold'),
        color: theme.colors.Text.text900,
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        width: '100%',
        marginBottom: theme.spacing.spacing.s4,
        gap: theme.spacing.spacing.s1,
    },
    headerIcon: {
        marginRight: theme.spacing.spacing.s1,
    },
    categoryTitle: {
        ...theme.textVariants.text('xs', 'regular'),
        color: theme.colors.Text.text900,
        marginBottom: theme.spacing.spacing.s2,
        alignSelf: 'flex-start',
    },
    categoryContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        marginBottom: theme.spacing.spacing.s5,
    },
    input: {
        width: '100%',
        height: 120,
        backgroundColor: theme.colors.Background.background50,
        color: theme.colors.Text.text900,
        borderColor: theme.colors.Background.background300,
        borderWidth: 1,
        borderRadius: theme.spacing.borderRadius.lg,
        padding: theme.spacing.spacing.s3,
        textAlignVertical: 'top',
        marginBottom: theme.spacing.spacing.s5,
        ...theme.textVariants.text('sm')
    },
    anonymousContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        marginBottom: theme.spacing.spacing.s5,
        marginLeft: theme.spacing.spacing.s1,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderRadius: theme.spacing.borderRadius.sm,
        borderWidth: 1,
        borderColor: theme.colors.Text.text500,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: theme.spacing.spacing.s2,
    },
    checkboxChecked: {
        backgroundColor: theme.colors.Primary.primary500,
    },
    anonymousText: {
        ...theme.textVariants.text('sm'),
        color: theme.colors.Text.text900,
    },
    submitButton: {
        backgroundColor: theme.colors.Primary.primary500,
        paddingVertical: theme.spacing.spacing.s3,
        paddingHorizontal: theme.spacing.spacing.s6,
        borderRadius: theme.spacing.borderRadius.full,
        elevation: 2,
        alignSelf: 'center'
    },
    submitButtonText: {
        ...theme.textVariants.button,
        color: theme.colors.Text.text900,
        fontWeight: 'bold'
    },
    safeArea: {
        flex: 1,
    }
  }), [theme]);

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={resetFormAndClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.centeredView}>
            <View style={styles.modalView}>
                <TouchableOpacity style={styles.closeButton} onPress={resetFormAndClose}>
                    <X color={theme.colors.Text.text900} size={24} />
                </TouchableOpacity>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={styles.headerContainer}>
                        <Text style={styles.modalTitle}>We'd love to hear from you!</Text>
                    </View>
                    
                    <Text style={styles.categoryTitle}>Select relevant categories (optional)</Text>
                    <View style={styles.categoryContainer}>
                        {feedbackCategories.map((category) => (
                        <Label
                            key={category}
                            text={category}
                            variant={selectedCategories.includes(category) ? 'solid' : 'outline'}
                            colorScheme={selectedCategories.includes(category) ? 'primary' : 'muted'}
                            onPress={() => handleCategorySelect(category)}
                            containerStyle={{ margin: 4 }}
                        />
                        ))}
                    </View>

                    <TextInput
                            style={styles.input}
                            placeholder="Share your thoughts..."
                            placeholderTextColor={theme.colors.Text.text500}
                            multiline
                            value={feedbackText}
                            onChangeText={setFeedbackText}
                            maxLength={2000}
                    />
                    <TouchableOpacity style={styles.anonymousContainer} onPress={() => setIsAnonymous(!isAnonymous)}>
                        <View style={[styles.checkbox, isAnonymous && styles.checkboxChecked]}>
                            {isAnonymous && <Check color={theme.colors.Text.text0} size={16} />}
                        </View>
                        <Text style={styles.anonymousText}>Send anonymously</Text>
                    </TouchableOpacity>
                    <Button
                        title="Submit"
                        onPress={handleSubmit}
                        disabled={!feedbackText.trim() || loading}
                        variant='neutral'
                    />
                </ScrollView>
            </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default FeedbackModal;
