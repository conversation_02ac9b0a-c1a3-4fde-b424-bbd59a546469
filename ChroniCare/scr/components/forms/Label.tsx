import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { X } from 'lucide-react-native';
import { useTheme } from '../../context/themeContext'; // Adjust path as needed
import { Theme } from '../../theme/theme'; // Adjust path as needed

type ColorScheme = 'primary' | 'secondary' | 'neutral' | 'success' | 'muted';
type Variant = 'solid' | 'outline';

export interface LabelProps {
  text: string;
  variant?: Variant;
  colorScheme?: ColorScheme;
  iconLeft?: React.ReactElement;
  iconRight?: React.ReactElement;
  onRemove?: () => void;
  onPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

const Label: React.FC<LabelProps> = ({
  text,
  variant = 'solid',
  colorScheme = 'primary',
  iconLeft,
  iconRight,
  onRemove,
  onPress,
  containerStyle,
  textStyle,
}) => {
  const { theme } = useTheme();

  // Calculate colors outside StyleSheet.create but within useMemo for direct access
  const { componentStyles, derivedColors } = useMemo(() => {
    const getColors = () => {
      let backgroundColor: string;
      let textAndIconColor: string;
      let borderColor: string;

      const primaryColors = {
        solidBg: theme.colors.Primary.primary100,
        solidTextAndIcon: theme.colors.Text.text900,
        outlineBorderAndTextIcon: theme.colors.Primary.primary500,
      };
      const secondaryColors = {
        solidBg: theme.colors.Secondary.secondary100,
        solidTextAndIcon: theme.colors.Secondary.secondary700,
        outlineBorderAndTextIcon: theme.colors.Secondary.secondary500,
      };
      const successColors = {
        solidBg: theme.colors.Success.success100,
        solidTextAndIcon: theme.colors.Success.success700,
        outlineBorderAndTextIcon: theme.colors.Success.success500,
      };
      const neutralColors = {
        solidBg: theme.colors.Background.background900,
        solidTextAndIcon: theme.colors.Text.text0,
        outlineBorderAndTextIcon: theme.colors.Text.text700, // Text color for outline
        outlineBorder: theme.colors.Background.background100,
      };
      const mutedColors = {
        solidBg: theme.colors.Background.backroundMuted,
        solidTextAndIcon: theme.colors.Text.text900,
        outlineBorderAndTextIcon: theme.colors.Text.text900,
        outlineBorder: theme.colors.Background.background300,
      };

      let schemeSet;
      switch (colorScheme) {
        case 'primary': schemeSet = primaryColors; break;
        case 'secondary': schemeSet = secondaryColors; break;
        case 'success': schemeSet = successColors; break;
        case 'neutral': schemeSet = neutralColors; break;
        case 'muted': schemeSet = mutedColors; break;
        default: schemeSet = primaryColors;
      }

      if (variant === 'solid') {
        backgroundColor = schemeSet.solidBg;
        textAndIconColor = schemeSet.solidTextAndIcon;
        borderColor = colorScheme === 'muted' ? mutedColors.outlineBorder : 'transparent';
      } else { // outline
        backgroundColor = theme.colors.Background.background0; // Or could be transparent
        textAndIconColor = schemeSet.outlineBorderAndTextIcon;
        borderColor = colorScheme === 'neutral' ? neutralColors.outlineBorder : schemeSet.outlineBorderAndTextIcon;
      }
      return { backgroundColor, textAndIconColor, borderColor };
    };

    const calculatedColors = getColors();

    const styles = StyleSheet.create({
      container: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        paddingVertical: theme.spacing.spacing.s1,
        paddingHorizontal: theme.spacing.spacing.s2,
        borderRadius: theme.spacing.borderRadius.full,
        borderWidth: theme.spacing.borderWidth.s1,
        backgroundColor: calculatedColors.backgroundColor,
        borderColor: calculatedColors.borderColor,
      },
      text: {
        ...theme.textVariants.text('xs', 'regular'),
        color: calculatedColors.textAndIconColor,
        marginHorizontal: (iconLeft || iconRight || onRemove) ? theme.spacing.spacing.s1_5 : 0,
      },
      iconContainer: {
        // If specific styling for icon container is needed
      },
    });
    return { componentStyles: styles, derivedColors: calculatedColors };
  }, [theme, variant, colorScheme, iconLeft, iconRight, onRemove]);

  // Clone iconLeft and apply the correct color
  const clonedIconLeft = iconLeft ? React.cloneElement(iconLeft, {
    ...(iconLeft.props || {}),
    color: derivedColors.textAndIconColor,
  } as any) : null;

  // Clone iconRight and apply the correct color
  const clonedIconRight = iconRight ? React.cloneElement(iconRight, {
    ...(iconRight.props || {}),
    color: derivedColors.textAndIconColor,
  } as any) : null;

  return (
    <TouchableOpacity 
      style={[componentStyles.container, containerStyle]}
      onPress={onPress}
      activeOpacity={onPress ? 0.5 : 1}
      disabled={!onPress}
    >
      {clonedIconLeft && <View style={componentStyles.iconContainer}>{clonedIconLeft}</View>}
      <Text style={[componentStyles.text, textStyle]}>{text}</Text>
      {clonedIconRight && <View style={componentStyles.iconContainer}>{clonedIconRight}</View>}
      {onRemove && !iconLeft && !iconRight && (
        <TouchableOpacity onPress={onRemove} style={componentStyles.iconContainer}>
          <X size={16} color={derivedColors.textAndIconColor} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

export default Label; 