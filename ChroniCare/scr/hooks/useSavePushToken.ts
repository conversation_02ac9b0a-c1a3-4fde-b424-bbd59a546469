import { useMutation } from '@apollo/client';
import { useCallback } from 'react';
import { SAVE_PUSH_TOKEN, REMOVE_PUSH_TOKEN } from '../graphql/queries';

interface SavePushTokenInput {
  token: string;
}

interface SavePushTokenResponse {
  savePushToken: boolean;
}

interface RemovePushTokenResponse {
  removePushToken: boolean;
}

export const useSavePushToken = () => {
  const [savePushTokenMutation, { loading: saveLoading, error: saveError }] = useMutation<
    SavePushTokenResponse,
    { input: SavePushTokenInput }
  >(SAVE_PUSH_TOKEN);

  const [removePushTokenMutation, { loading: removeLoading, error: removeError }] = useMutation<
    RemovePushTokenResponse,
    { token: string }
  >(REMOVE_PUSH_TOKEN);

  const savePushToken = useCallback(async (token: string): Promise<boolean> => {
    try {
      const result = await savePushTokenMutation({
        variables: {
          input: { token }
        }
      });
      return result.data?.savePushToken ?? false;
    } catch (error) {
      console.error('Failed to save push token:', error);
      throw error;
    }
  }, [savePushTokenMutation]);

  const removePushToken = useCallback(async (token: string): Promise<boolean> => {
    try {
      const result = await removePushTokenMutation({
        variables: { token }
      });
      return result.data?.removePushToken ?? false;
    } catch (error) {
      console.error('Failed to remove push token:', error);
      throw error;
    }
  }, [removePushTokenMutation]);

  return {
    savePushToken,
    removePushToken,
    loading: saveLoading || removeLoading,
    saveLoading,
    removeLoading,
    error: saveError || removeError,
    saveError,
    removeError,
  };
};