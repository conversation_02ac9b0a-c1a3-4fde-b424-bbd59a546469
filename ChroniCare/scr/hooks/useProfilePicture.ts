import { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { GET_ME, CREATE_PROFILE_IMAGE_UPLOAD_URL, UPDATE_USER_PROFILE_IMAGE, REMOVE_USER_PROFILE_IMAGE } from '../graphql/queries';
import { useUser, User } from '../context/userContext';

export interface UseProfilePictureResult {
  user: User | null;
  existingPhotoURL: string | null;
  isLoading: boolean;
  error: any;
  refreshProfilePicture: () => void;
  uploadProfilePicture: (imageUri: string, mimeType: string) => Promise<void>;
  removeProfilePicture: () => Promise<void>;
  isUploading: boolean;
  isRemoving: boolean;
}

interface UseProfilePictureOptions {
  onboardingStoreApi?: {
    setProfilePicture: (imageUri: string | null) => void;
  };
}

export const useProfilePicture = (options?: UseProfilePictureOptions): UseProfilePictureResult => {
  const { user, loading, error, refetchUser: refetch } = useUser();
  const setProfilePicture = options?.onboardingStoreApi?.setProfilePicture;
  const [hasSetInitialPhoto, setHasSetInitialPhoto] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  // Mutations for uploading profile picture
  const [createUploadUrl] = useMutation(CREATE_PROFILE_IMAGE_UPLOAD_URL);
  const [updateUserProfileImage] = useMutation(UPDATE_USER_PROFILE_IMAGE, {
    refetchQueries: [{ query: GET_ME }],
  });
  const [removeUserProfileImage] = useMutation(REMOVE_USER_PROFILE_IMAGE, {
    refetchQueries: [{ query: GET_ME }],
  });
  // Set initial profile picture from database if available
  useEffect(() => {
    if (user?.photoURL && !hasSetInitialPhoto && setProfilePicture) {
      setProfilePicture(user.photoURL);
      setHasSetInitialPhoto(true);
    }
  }, [user?.photoURL, setProfilePicture, hasSetInitialPhoto]);

  const refreshProfilePicture = () => {
    refetch();
  };

  const uploadProfilePicture = async (
    imageUri: string,
    mimeType: string,
  ): Promise<void> => {
    try {
      setIsUploading(true);

      // Step 1: Get signed upload URL from your backend
      const { data: uploadData } = await createUploadUrl({
        variables: { contentType: mimeType },
      });
      const { signedUrl, publicUrl } = uploadData.createProfileImageUploadUrl;

      console.log('🔍 Signed URL:', signedUrl);
      console.log('🔍 Public URL:', publicUrl);

      // Step 2: Get the image data directly as ArrayBuffer (React Native compatible)
      const response = await fetch(imageUri);
      const arrayBuffer = await response.arrayBuffer();
      console.log('🔍 ArrayBuffer size:', arrayBuffer.byteLength);
      

      // Step 3: Upload the ArrayBuffer to Google Cloud Storage using the signed URL
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: arrayBuffer,
        headers: {
          'Content-Type': mimeType,
        },
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        throw new Error(
          `Failed to upload image to storage: ${uploadResponse.status} - ${responseText}`,
        );
      }

      // Step 4: Update the user's profile with the public URL of the uploaded image
      await updateUserProfileImage({
        variables: { profileImageUrl: publicUrl },
      });
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  const removeProfilePicture = async (): Promise<void> => {
    try {
      setIsRemoving(true);

      // Remove profile picture from backend
      await removeUserProfileImage();

      // Update local state
      console.log('Profile picture removed successfully');
      if (setProfilePicture) {
        setProfilePicture(null);
      }
    } catch (error) {
      console.error('Error removing profile picture:', error);
      throw error;
    } finally {
      setIsRemoving(false);
    }
  };

  return {
    user,
    existingPhotoURL: user?.photoURL || null,
    isLoading: loading,
    error,
    refreshProfilePicture,
    uploadProfilePicture,
    removeProfilePicture,
    isUploading,
    isRemoving,
  };
}; 

