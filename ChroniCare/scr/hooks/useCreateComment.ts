import { useMutation, gql, useApolloClient } from '@apollo/client';
import { useUser } from '../context/userContext';
import { CREATE_COMMENT, GET_COMMENTS, GET_THREADS } from '../graphql/queries';
import { THREAD_FRAGMENT } from '../graphql/fragments';
import { Alert } from 'react-native';
import { Comment } from '../components/community/comments/CommentCard';

interface CreateCommentInput {
  threadId: string;
  content: string;
  parentCommentId?: string;
}

export const useCreateComment = () => {
  const { user } = useUser();
  const client = useApolloClient();

  // Helper to get communityId from thread cache
  const getCommunityIdFromCache = (threadId: string): string => {
    try {
      const threadData = client.readFragment({
        id: client.cache.identify({ __typename: 'Thread', _id: threadId }),
        fragment: THREAD_FRAGMENT,
      });
      return threadData?.communityId || 'temp-community-id';
    } catch (error) {
      console.warn('Could not read communityId from cache:', error);
      return 'temp-community-id';
    }
  };

  // Helper function to safely update cache with proper cache key matching
  const updateCacheAfterComment = (cache: any, newComment: any) => {
    const threadId = newComment.threadId;

    try {
      console.log('Updating cache for comment:', newComment._id, 'in thread:', threadId);

      // Try direct cache field update first
      try {
        const cacheId = 'ROOT_QUERY';
        const fieldName = 'comments';
        const fieldArgs = { threadId, limit: 50, offset: 0 };

        // Read existing comments from cache
        const existingData = cache.readQuery({
          query: GET_COMMENTS,
          variables: fieldArgs,
        });

        if (existingData?.comments) {
          console.log('Found existing comments in cache, updating directly');

          const existingComments = existingData.comments;
          const existingIds = existingComments.map((comment: any) => comment._id);

          if (!existingIds.includes(newComment._id)) {
            let updatedComments;
            if (newComment.parentCommentId) {
              // Replies go at the end to maintain chronological order
              console.log('Adding reply to end of list');
              updatedComments = [...existingComments, newComment];
            } else {
              // Top-level comments go at the beginning
              console.log('Adding top-level comment to beginning of list');
              updatedComments = [newComment, ...existingComments];
            }

            // Write updated comments back to cache
            cache.writeQuery({
              query: GET_COMMENTS,
              variables: fieldArgs,
              data: { comments: updatedComments },
            });

            console.log('Successfully updated comments cache directly');
          } else {
            console.log('Comment already exists in cache, skipping direct update');
          }
        } else {
          console.log('No existing comments found in cache, using cache.modify fallback');

          // Fallback to cache.modify
          cache.modify({
            fields: {
              comments(existingComments = [], { args, toReference }) {
                console.log('Cache modify fallback called with args:', args);

                // More flexible matching - check if this is for the right thread
                const isRightThread = !args || args.threadId === threadId;

                if (isRightThread) {
                  const newCommentRef = toReference(newComment);
                  console.log('Adding comment to cache via modify:', newComment._id);

                  // Prevent duplicates
                  const existingIds = existingComments.map((comment: any) =>
                    cache.readField('_id', comment)
                  );

                  if (!existingIds.includes(newComment._id)) {
                    if (newComment.parentCommentId) {
                      console.log('Adding reply to end of list via modify');
                      return [...existingComments, newCommentRef];
                    } else {
                      console.log('Adding top-level comment to beginning of list via modify');
                      return [newCommentRef, ...existingComments];
                    }
                  }
                }
                return existingComments;
              },
            },
          });
        }
      } catch (cacheError) {
        console.error('Direct cache update failed, using modify as fallback:', cacheError);

        // Final fallback to cache.modify
        cache.modify({
          fields: {
            comments(existingComments = [], { args, toReference }) {
              console.log('Final fallback cache modify called with args:', args);

              const newCommentRef = toReference(newComment);

              // Prevent duplicates
              const existingIds = existingComments.map((comment: any) =>
                cache.readField('_id', comment)
              );

              if (!existingIds.includes(newComment._id)) {
                if (newComment.parentCommentId) {
                  return [...existingComments, newCommentRef];
                } else {
                  return [newCommentRef, ...existingComments];
                }
              }
              return existingComments;
            },
          },
        });
      }

      // Update thread comment count
      const threadCacheId = cache.identify({ __typename: 'Thread', _id: threadId });
      if (threadCacheId) {
        cache.modify({
          id: threadCacheId,
          fields: {
            commentCount(existingCount = 0) {
              console.log('Updating thread comment count from', existingCount, 'to', existingCount + 1);
              return existingCount + 1;
            },
          },
        });
      }

      // Update parent comment reply count for replies
      if (newComment.parentCommentId) {
        const parentCommentId = cache.identify({
          __typename: 'Comment',
          _id: newComment.parentCommentId
        });

        if (parentCommentId) {
          cache.modify({
            id: parentCommentId,
            fields: {
              replyCount(existingCount = 0) {
                console.log('Updating parent comment reply count from', existingCount, 'to', existingCount + 1);
                return existingCount + 1;
              },
            },
          });
        }
      }

      console.log('Cache update completed successfully');
      return true;
    } catch (error) {
      console.error('Cache update failed:', error);
      return false;
    }
  };

  const [createCommentMutation, { loading, error }] = useMutation(CREATE_COMMENT, {
    optimisticResponse: (variables) => {
      if (!user) {
        console.log('No user available for optimistic response');
        return {};
      }

      const { input } = variables;
      const optimisticId = `temp-comment-${Date.now()}-${Math.random()}`;

      console.log('Creating optimistic response for comment:', optimisticId);

      return {
        createComment: {
          __typename: 'Comment',
          _id: optimisticId,
          threadId: input.threadId,
          communityId: getCommunityIdFromCache(input.threadId), // Get real communityId from cache
          author: {
            __typename: 'Author',
            authorId: input.author.authorId,
            displayName: input.author.displayName,
            condition: input.author.condition,
            userType: input.author.userType,
            photoURL: input.author.photoURL,
          },
          content: input.content,
          createdAt: new Date().toISOString(),
          parentCommentId: input.parentCommentId || null,
          replyCount: 0,
          reactionCounts: {
            __typename: 'ReactionCounts',
            love: 0,
            withYou: 0,
            funny: 0,
            insightful: 0,
            poop: 0,
          },
          myReaction: null,
        },
      };
    },
    update: (cache, { data }) => {
      if (!data?.createComment) return;

      const newComment = data.createComment;
      const success = updateCacheAfterComment(cache, newComment);

      // Fallback: refetch if cache update failed
      if (!success) {
        console.warn('Cache update failed, falling back to refetch');
        client.refetchQueries({
          include: [GET_COMMENTS],
        });
      }
    },
    onError: (error, clientOptions) => {
      console.error('Comment creation failed:', error);

      // Clean up optimistic response on error
      const variables = clientOptions?.variables;
      if (variables?.input?.threadId) {
        try {
          // Force refetch to get correct state
          client.refetchQueries({
            include: [GET_COMMENTS],
          });
        } catch (refetchError) {
          console.error('Failed to refetch after error:', refetchError);
        }
      }

      Alert.alert('Error Creating Comment', error.message);
    },
    // Add error policy to handle partial failures gracefully
    errorPolicy: 'all',
  });

  const handleCreateComment = async (input: CreateCommentInput) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to comment.');
      return;
    }

    if (!input.content.trim()) {
      Alert.alert('Error', 'Comment cannot be empty.');
      return;
    }

    const authorInput = {
      authorId: user.id,
      displayName: user.displayName || user.firstName || user.email || 'Anonymous',
      condition: user.condition || '',
      userType: user.userType || '',
      photoURL: user.photoURL || '',
    };

    try {
      console.log('Executing createComment mutation with input:', {
        ...input,
        author: authorInput,
      });

      const result = await createCommentMutation({
        variables: {
          input: {
            ...input,
            author: authorInput,
          },
        },
      });

      console.log('Comment mutation completed successfully:', result);
      return result;
    } catch (error) {
      console.error('Failed to create comment:', error);
      // Error is already handled by onError callback
      throw error;
    }
  };

  return { 
    createComment: handleCreateComment, 
    loading, 
    error 
  };
};

