import AsyncStorage from "@react-native-async-storage/async-storage";
import { useState, useEffect } from "react";
import { Alert } from "react-native";

const DRAFT_KEY = "createPostDraft";

export interface PostDraft {
  title: string;
  content: string;
}

export const useDraft = () => {
  const [draft, setDraft] = useState<PostDraft | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadDraft = async () => {
      try {
        const draftJson = await AsyncStorage.getItem(DRAFT_KEY);
        if (draftJson) {
          setDraft(JSON.parse(draftJson));
        }
      } catch (error) {
        console.error("Failed to load draft", error);
        Alert.alert("Error", "Failed to load draft.");
      } finally {
        setIsLoaded(true);
      }
    };

    loadDraft();
  }, []);

  const saveDraft = async (title: string, content: string) => {
    try {
      const draftData: PostDraft = { title, content };
      await AsyncStorage.setItem(DRAFT_KEY, JSON.stringify(draftData));
    } catch (error) {
      console.error("Failed to save draft", error);
      Alert.alert("Error", "Failed to save draft.");
    }
  };

  const clearDraft = async () => {
    try {
      await AsyncStorage.removeItem(DRAFT_KEY);
      setDraft(null);
    } catch (error) {
      console.error("Failed to clear draft", error);
      Alert.alert("Error", "Failed to clear draft.");
    }
  };

  return { draft, isLoaded, saveDraft, clearDraft, setDraft };
}; 