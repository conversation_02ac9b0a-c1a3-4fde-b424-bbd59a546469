import { useMutation } from '@apollo/client';
import { useRouter } from 'expo-router';
import { DELETE_THREAD, GET_THREADS, GET_THREAD } from '../graphql/queries';
import { Alert } from 'react-native';

export const useDeleteThread = () => {
  const router = useRouter();

  const [deleteThreadMutation, { loading, error }] = useMutation(DELETE_THREAD, {
    onCompleted: (data) => {
      if (data.deleteThread) {
        Alert.alert(
          'Thread Deleted',
          'Your thread has been successfully deleted.',
          [{ text: 'OK' }]
        );
      }
    },
    onError: (error) => {
      console.error('Delete thread error:', error);
      Alert.alert(
        'Delete Failed', 
        error.message || 'Failed to delete thread. Please try again.',
        [{ text: 'OK' }]
      );
    },
    update: (cache, { data }) => {
      if (data?.deleteThread) {
        const deletedThreadId = data.deleteThread._id;
        
        // Remove from all GET_THREADS queries in cache
        cache.modify({
          fields: {
            threads(existingThreads = [], { readField }) {
              return existingThreads.filter(
                (threadRef: any) => readField('_id', threadRef) !== deletedThreadId
              );
            },
          },
        });

        // Remove the specific thread query from cache
        cache.evict({
          id: cache.identify({ __typename: 'Thread', _id: deletedThreadId }),
        });

        // Clean up any orphaned references
        cache.gc();
      }
    },
  });

  const deleteThread = async (threadId: string, currentPath?: string) => {
    try {
      await deleteThreadMutation({
        variables: { threadId },
      });

      // Navigate away if user is on the deleted thread's page
      if (currentPath && currentPath.includes(`/community/${threadId}`)) {
        router.back();
      }
    } catch (error) {
      console.error('Error deleting thread:', error);
    }
  };

  const deleteThreadWithConfirmation = (threadId: string, currentPath?: string) => {
    Alert.alert(
      'Delete Thread',
      'Are you sure you want to delete this thread? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteThread(threadId, currentPath),
        },
      ]
    );
  };

  return { 
    deleteThread: deleteThreadWithConfirmation, 
    deleteThreadWithoutConfirmation: deleteThread,
    loading, 
    error 
  };
};