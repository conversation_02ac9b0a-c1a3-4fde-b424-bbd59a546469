import { useFragment, useQuery } from '@apollo/client';
import { GET_THREADS } from '../graphql/queries';
import { THREAD_FRAGMENT, type Thread } from '../graphql/fragments';

export const useThread = (threadId: string): Thread | null => {
  // Try to get from fragment first (fastest)
  const { data: fragmentData, complete } = useFragment({
    fragment: THREAD_FRAGMENT,
    from: {
      __typename: 'Thread',
      _id: threadId,
    },
  });

  // Fallback: get from query cache if fragment incomplete
  const { data: queryData } = useQuery(GET_THREADS, {
    variables: { limit: 10, communityId: undefined },
    fetchPolicy: 'cache-only',
    skip: complete, // Skip if fragment already has complete data
  });

  // Return fragment data if available, otherwise search in query data
  if (fragmentData && complete) {
    return fragmentData as Thread;
  }

  if (queryData?.threads) {
    const thread = queryData.threads.find((t: Thread) => t._id === threadId);
    return thread || null;
  }

  return null;
};