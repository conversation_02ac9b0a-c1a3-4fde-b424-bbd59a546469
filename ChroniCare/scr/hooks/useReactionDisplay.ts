import { useMemo } from 'react';
import { ReactionCount } from '../graphql/fragments';
import { Heart, HeartHandshake, Laugh, Lightbulb, IceCream } from 'lucide-react-native';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

export const iconMapping: Record<
  ReactionType,
  { icon: React.ElementType }
> = {
  love: { icon: Heart },
  withYou: { icon: HeartHandshake },
  funny: { icon: Laugh },
  insightful: { icon: Lightbulb },
  poop: { icon: IceCream },
};

export const useReactionDisplay = (reactionCounts: ReactionCount | null) => {
  const totalReactions = useMemo(() => {
    if (!reactionCounts) return 0;
    
    // Ensure all values are numbers and sum them up
    const total = (reactionCounts.love || 0) + 
                  (reactionCounts.withYou || 0) + 
                  (reactionCounts.funny || 0) + 
                  (reactionCounts.insightful || 0) + 
                  (reactionCounts.poop || 0);
    
    return total;
  }, [reactionCounts]);

  const displayedReactions = useMemo(() => {
    if (!reactionCounts || totalReactions === 0) {
      return [{
        key: 'love' as ReactionType,
        count: 0,
        ...iconMapping['love'],
      }];
    }
    
    return (Object.entries(reactionCounts) as Array<[ReactionType, number]>)
      .map(([key, count]) => ({
        key: key as ReactionType,
        count: count || 0,
        ...iconMapping[key as ReactionType],
      }))
      .filter(reaction => reaction.count > 0)
      .sort((a, b) => b.count - a.count);
  }, [reactionCounts, totalReactions]);

  return {
    totalReactions,
    displayedReactions,
    iconMapping,
  };
};
