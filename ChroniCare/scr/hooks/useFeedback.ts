import { gql, useMutation } from '@apollo/client';
import { useUser } from '../context/userContext';

const CREATE_FEEDBACK = gql`
  mutation CreateFeedback($categories: [String!]!, $text: String!, $name: String) {
    createFeedback(createFeedbackInput: { categories: $categories, text: $text, name: $name })
  }
`;

export const useFeedback = () => {
  const [createFeedbackMutation, { loading, error }] = useMutation(CREATE_FEEDBACK);
  const { user } = useUser();
  const submitFeedback = async (categories: string[], text: string, isAnonymous: boolean) => {
    try {
      const response = await createFeedbackMutation({
        variables: {
          categories,
          text,
          name: isAnonymous ? 'Anonymous' : user?.displayName || user?.firstName + ' ' + user?.lastName,
        },
      });

      const success = response.data?.createFeedback || false;
      if (success) {
        console.log('Feedback submitted successfully.');
      } else {
        console.warn('Feedback submission failed or returned false.');
      }
      
      return success;
    } catch (e) {
      console.error('Error submitting feedback:', e);
      throw e;
    }
  };

  return { submitFeedback, loading, error };
};
