import { useNotification } from "../context/notificationsContext";

export const useNotifications = () => {
	const {
		expoPushToken,
		notification,
		error,
		tokenSaveLoading,
		tokenSaveError,
		requestPermissions,
		isRequestingPermissions
	} = useNotification();



	return {
		expoPushToken,
		notification,
		error,
		tokenSaveLoading,
		tokenSaveError, 
		requestPermissions,
		isRequestingPermissions,
		// Convenience properties for checking states
		hasToken: !!expoPushToken,
		isTokenSaving: tokenSaveLoading,
		hasTokenSaveError: !!tokenSaveError,
		hasNotificationError: !!error,
	};
};