import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    useRef,
    ReactNode,
  } from "react";
  import * as Notifications from "expo-notifications";
  import { EventSubscription } from "expo-notifications";
  import { registerForPushNotificationsAsync } from "../utils/registerForPushNotificationsAsync";
  import { useSavePushToken } from "../hooks/useSavePushToken";
  import { useAuth } from "./authContext";
  import { useRouter } from "expo-router";
  import { useApolloClient } from "@apollo/client";
  import { GET_THREAD } from "../graphql/queries";
  import { Thread } from "../graphql/fragments";

  interface NotificationContextType {
    expoPushToken: string | null;
    notification: Notifications.Notification | null;
    error: Error | null;
    tokenSaveLoading: boolean;
    tokenSaveError: Error | null;
    requestPermissions: () => Promise<void>;
    isRequestingPermissions: boolean;
    newlyFetchedThread: Thread | null;
    clearNewlyFetchedThread: () => void;
    isFromNotification: boolean;
    clearNotificationFlag: () => void;
  }
  
  const NotificationContext = createContext<NotificationContextType | undefined>(
    undefined
  );
  
  export const useNotification = () => {
    const context = useContext(NotificationContext);
    if (context === undefined) {
      throw new Error(
        "useNotification must be used within a NotificationProvider"
      );
    }
    return context;
  };
  
  interface NotificationProviderProps {
    children: ReactNode;
  }
  
  export const NotificationProvider: React.FC<NotificationProviderProps> = ({
    children,
  }) => {
    const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
    const [notification, setNotification] =
      useState<Notifications.Notification | null>(null);
    const [error, setError] = useState<Error | null>(null);
    const [tokenSaveError, setTokenSaveError] = useState<Error | null>(null);
    const [isRequestingPermissions, setIsRequestingPermissions] = useState(false);
    const [newlyFetchedThread, setNewlyFetchedThread] = useState<Thread | null>(null);
    const [isFromNotification, setIsFromNotification] = useState(false);
  const [lastNotificationThreadId, setLastNotificationThreadId] = useState<string | null>(null);

    const router = useRouter();
    const client = useApolloClient();

    const notificationListener = useRef<EventSubscription | null>(null);
    const responseListener = useRef<EventSubscription | null>(null);
    const { user } = useAuth();
    const { savePushToken, saveLoading } = useSavePushToken();
    
    const clearNewlyFetchedThread = () => {
      setNewlyFetchedThread(null);
    };

    const clearNotificationFlag = () => {
      setIsFromNotification(false);
      setLastNotificationThreadId(null);
    };
  
    // Manual function to request permissions (called from bottom sheet)
    const requestPermissions = async () => {
      setIsRequestingPermissions(true);
      setError(null);
      setTokenSaveError(null);

      try {
        const token = await registerForPushNotificationsAsync();
        setExpoPushToken(token);

        // The token will be saved by the useEffect below when expoPushToken state is updated.
        // This avoids a double-call and simplifies logic.
        
      } catch (error) {
        console.error('Failed to register for push notifications:', error);
        setError(error as Error);
      } finally {
        setIsRequestingPermissions(false);
      }
    };

    // Set up notification listeners on mount
    useEffect(() => {
      
      notificationListener.current =
        Notifications.addNotificationReceivedListener((notification) => {
          setNotification(notification);
        });
  
      responseListener.current =
        Notifications.addNotificationResponseReceivedListener(async (response) => {
          const { data } = response.notification.request.content;
          
          if (data && data.threadId) {
            try {
              // Properly invalidate comments cache for this specific thread
              console.log('Invalidating comments cache for thread:', data.threadId);

              // More comprehensive cache invalidation strategy
              const invalidateCommentsCache = (threadId: string) => {
                try {
                  // Method 1: Evict the specific query used by the thread detail page
                  try {
                    client.cache.evict({
                      id: 'ROOT_QUERY',
                      fieldName: 'comments',
                      args: { threadId, limit: 50, offset: 0 }
                    });
                    console.log('Successfully evicted main comments cache');
                  } catch (e) {
                    console.warn('Failed to evict main comments cache:', e);
                  }

                  // Method 2: Evict common variations that might exist
                  const commonLimits = [20, 50, 100];
                  const commonOffsets = [0];

                  commonLimits.forEach(limit => {
                    commonOffsets.forEach(offset => {
                      try {
                        client.cache.evict({
                          id: 'ROOT_QUERY',
                          fieldName: 'comments',
                          args: { threadId, limit, offset }
                        });
                      } catch (e) {
                        // Ignore individual eviction failures
                      }
                    });
                  });

                  // Method 3: Evict legacy queries without explicit limit/offset
                  try {
                    client.cache.evict({
                      id: 'ROOT_QUERY',
                      fieldName: 'comments',
                      args: { threadId }
                    });
                  } catch (e) {
                    // Ignore eviction failure
                  }

                  console.log('Comments cache invalidation completed');


                } catch (error) {
                  console.warn('Cache invalidation failed:', error);
                }
              };

              invalidateCommentsCache(data.threadId as string);

              // Force garbage collection to clean up orphaned references
              client.cache.gc();

              // Pre-fetch thread data for better UX
              const { data: threadData } = await client.query({
                query: GET_THREAD,
                variables: { id: data.threadId },
                fetchPolicy: 'network-only',
              });

              if (threadData?.thread) {
                setNewlyFetchedThread(threadData.thread);
              }

              // Set flag to indicate navigation from notification
              setIsFromNotification(true);
              setLastNotificationThreadId(data.threadId as string);

              router.push(`/community/${data.threadId}`);
            } catch (error) {
              console.error('Failed to pre-fetch thread data, navigating anyway.', error)
              // Still invalidate cache and set flag even if pre-fetch fails
              console.log('Pre-fetch failed, but still invalidating comments cache for thread:', data.threadId);

              // Fallback cache invalidation
              try {
                client.cache.evict({
                  id: 'ROOT_QUERY',
                  fieldName: 'comments',
                  args: { threadId: data.threadId as string }
                });
                client.cache.gc();
              } catch (evictError) {
                console.warn('Fallback cache eviction failed:', evictError);
              }

              setIsFromNotification(true);
              setLastNotificationThreadId(data.threadId as string);
              router.push(`/community/${data.threadId}`);
            }
          }
        });
      return () => {
        if (notificationListener.current) {
          notificationListener.current.remove();
        }
        if (responseListener.current) {
          responseListener.current.remove();
        }
      };
    }, [client, router]);

    // Effect to save token when user authentication changes
    useEffect(() => {
      if (expoPushToken && user) {
        savePushToken(expoPushToken).catch((error) => {
          console.error('Failed to save push token after user auth:', error);
          setTokenSaveError(error);
        });
      }
    }, [user, expoPushToken, savePushToken]);

    return (
      <NotificationContext.Provider
        value={{
          expoPushToken,
          notification,
          error,
          tokenSaveLoading: saveLoading,
          tokenSaveError,
          requestPermissions,
          isRequestingPermissions,
          newlyFetchedThread,
          clearNewlyFetchedThread,
          isFromNotification,
          clearNotificationFlag,
        }}
      >
        {children}
      </NotificationContext.Provider>
    );
  };