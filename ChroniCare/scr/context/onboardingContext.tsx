import React, { createContext, useContext, useState, useEffect } from 'react';
import { useUser } from './userContext';

interface OnboardingContextType {
  hasCompletedOnboarding: boolean;
  isLoading: boolean;
  networkError: boolean;
}

const OnboardingContext = createContext<OnboardingContextType>({
  hasCompletedOnboarding: false,
  isLoading: true,
  networkError: false,
});

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({ children }) => {
  const { user, loading: userLoading, networkError } = useUser();
  const [lastKnownOnboardingState, setLastKnownOnboardingState] = useState<boolean | null>(null);

  useEffect(() => {
    if (user && !networkError) {
      setLastKnownOnboardingState(user.onboardingCompleted);
    }
  }, [user, networkError]);

  const hasCompletedOnboarding = networkError && lastKnownOnboardingState !== null
    ? lastKnownOnboardingState
    : user?.onboardingCompleted ?? false;

  return (
    <OnboardingContext.Provider
      value={{
        hasCompletedOnboarding,
        isLoading: userLoading,
        networkError,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => useContext(OnboardingContext);
