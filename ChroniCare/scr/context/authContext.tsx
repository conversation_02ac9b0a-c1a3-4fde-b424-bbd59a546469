import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import auth, {
  FirebaseAuthTypes,
  getAuth,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithCredential,
  signOut as authSignOut,
  GoogleAuthProvider,
  sendPasswordResetEmail
} from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { Alert } from 'react-native';
import { resetApolloCache } from '../apollo/apolloClient';

// Types
export interface AuthContextType {
  user: FirebaseAuthTypes.User | null;
  loading: boolean;
  initializing: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<FirebaseAuthTypes.UserCredential | void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
}

interface AuthProviderProps {
  children: ReactNode;
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Component
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null);
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true);

  // Get auth instance
  const authInstance = getAuth();

  // Configure Google Sign-In
  useEffect(() => {
    const configureGoogleSignIn = async () => {
      try {
        const webClientId = process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID;
        const iosClientId = process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID;

        if (!webClientId || !iosClientId) {
          console.error(
            'authContext.tsx: Google Sign-In is missing required environment variables (EXPO_PUBLIC_GOOGLE_CLIENT_ID or EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID).',
          );
          return;
        }

        GoogleSignin.configure({
          webClientId: webClientId, // Used for Android and web
          iosClientId: iosClientId, // Used for iOS
          offlineAccess: true, // Enable offline access for better token management
          hostedDomain: '', // Specify a hosted domain if needed
          forceCodeForRefreshToken: true, // Force code for refresh token
        });

        // Verify configuration
      } catch (error) {
        console.error('authContext.tsx: Error configuring Google Sign-In:', error);
      }
    };

    configureGoogleSignIn();
  }, []);

  // Handle auth state changes
  const onAuthStateChangedHandler = async (user: FirebaseAuthTypes.User | null) => {
    if (user) {
      try {
        // Force refresh the token to check if the user is still valid.
        await user.getIdToken(true);
        setUser(user);
      } catch (error: any) {
        console.error('authContext.tsx: Token refresh failed, user may be deleted.', error);
        // If token refresh fails, it means the user is no longer valid (e.g., deleted).
        // We should sign them out to clear the invalid local state.
        await signOut();
        setUser(null);
      }
    } else {
      setUser(null);
    }

    if (initializing) {
      setInitializing(false);
    }
  };

  useEffect(() => {
    const subscriber = onAuthStateChanged(authInstance, onAuthStateChangedHandler);
    return subscriber; // unsubscribe on unmount
  }, []);

  // Sign in with email and password
  const signIn = async (email: string, password: string): Promise<void> => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      await signInWithEmailAndPassword(authInstance, email, password);
    } catch (error: any) {
      console.error('Sign in error:', error);
      Alert.alert('Login Failed', error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string): Promise<FirebaseAuthTypes.UserCredential | void> => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    setLoading(true);
    try {
      const userCredential = await createUserWithEmailAndPassword(authInstance, email, password);
      return userCredential;
      // Firebase auth state change will be handled by onAuthStateChanged
    } catch (error: any) {
      console.error('Sign up error:', error);

      let errorMessage = 'An error occurred during registration';

      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'The email address is already registered. Please try to log in to your account.';
      } else if (error.message) {
        // Remove the error code in brackets (e.g., [auth/email-already-in-use])
        errorMessage = error.message.replace(/^\[.*?\]\s*/, '');
      }

      Alert.alert('Registration Failed', errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign in with Google
  const signInWithGoogle = async (): Promise<void> => {
    setLoading(true);
    try {

      // Check if device supports Google Play Services (iOS will skip this)
      try {
        await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      } catch (playServicesError: any) {
        // On iOS, this is expected to fail, so we continue
      }

      // Get the user's ID token
      const signInResult = await GoogleSignin.signIn();

      // Check if sign-in was cancelled
      if (signInResult.type === 'cancelled') {
        return; // Exit silently when user cancels
      }

      const idToken = signInResult.data?.idToken;

      if (!idToken) {
        console.error('No ID token in sign-in result:', signInResult);
        throw new Error('No ID token received from Google Sign-In. This often happens in iOS Simulator. Please try on a physical device.');
      }

      // Create a Google credential with the token
      const googleCredential = GoogleAuthProvider.credential(idToken);

      // Sign-in the user with the credential
      await signInWithCredential(authInstance, googleCredential);
    } catch (error: any) {
      console.error('Google Sign-In Error Details:', {
        code: error.code,
        message: error.message,
        stack: error.stack,
        fullError: error
      });

      Alert.alert('Error', error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Forgot password
  const forgotPassword = async (email: string): Promise<void> => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }
    setLoading(true);
    try {
      await sendPasswordResetEmail(authInstance, email);
      Alert.alert('Password Reset', 'A link to reset your password has been sent to your email.');
    } catch (error: any) {
      console.error('Forgot password error:', error);
      let errorMessage = 'An unexpected error occurred.';
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'This email is not registered. Please sign up or try another email.';
      } else if (error.message) {
        errorMessage = error.message.replace(/^\[.*?\]\s*/, '');
      }
      Alert.alert('Error', errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async (): Promise<void> => {
    try {
      await authSignOut(authInstance);

      // Reset Apollo cache to clear user-specific data
      await resetApolloCache();

      // Also sign out from Google (gracefully handles if not signed in)
      try {
        await GoogleSignin.signOut();
      } catch (googleError) {
        // Ignore Google sign out errors (user might not be signed in with Google)
        console.log('Google sign out not needed or failed:', googleError);
      }
    } catch (error: any) {
      console.error('Sign out error:', error);
      Alert.alert('Sign Out Failed', error.message);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    initializing,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    forgotPassword
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
