import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ApolloError, useQuery } from '@apollo/client';
import { GET_ME } from '../graphql/queries';
import { useAuth } from './authContext';

export interface User {
  id: string;
  firebaseUid: string;
  email: string | null;
  displayName: string | null;
  firstName: string | null;
  lastName: string | null;
  photoURL: string | null;
  onboardingCompleted: boolean;
  condition: string | null;
  userType: string | null;
  communities: {
    id: string;
    name: string;
    displayName: string;
  }[] | null;
}

interface UserContextType {
  user: User | null;
  loading: boolean;
  error?: ApolloError;
  networkError: boolean;
  refetchUser: () => Promise<any>;
  setSignupFlowActive: (isActive: boolean) => void;
}

export const UserContext = createContext<UserContextType | undefined>(undefined);

function isNetworkError(error: ApolloError): boolean {
  return (
    error.networkError !== null ||
    error.message?.includes('Network error') ||
    error.message?.includes('Failed to fetch') ||
    error.graphQLErrors?.some(
      (err) =>
        err.extensions?.code === 'NETWORK_ERROR' ||
        // Treat token validation errors as network errors, as they often happen
        // when the backend can't connect to Firebase or its own DB.
        err.message.includes('Invalid Firebase token')
    ) ||
    false
  );
}

export function UserProvider({ children }: { children: ReactNode }) {
  const { user: authUser, initializing: authInitializing } = useAuth();
  const [isSignupFlowActive, setSignupFlowActive] = useState(false);
  const { data, loading, error, refetch } = useQuery(GET_ME, {
    skip: !authUser || isSignupFlowActive,
    errorPolicy: 'all',
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true
  });

  const [user, setUser] = useState<User | null>(null);
  const [networkError, setNetworkError] = useState(false);

  useEffect(() => {
    if (loading) {
      // Don't change state while loading
      return;
    }

    if (data?.me) {
      setUser(data.me);
      setNetworkError(false);
    } else if (!authUser) {
      setUser(null);
      setNetworkError(false);
    } else if (error) {
      if (isNetworkError(error)) {
        setNetworkError(true);
      } else {
        // This case could happen if there's a GraphQL error but not a network error
        // for example a permissions issue. In this case, we might want to clear the user.
        setUser(null);
        setNetworkError(false);
      }
    }
  }, [data, loading, error, authUser]);

  const refetchUser = async () => {
    try {
      const result = await refetch();
      if (result.error) {
        throw result.error;
      }
      return result;
    } catch (err) {
      if (err instanceof ApolloError && isNetworkError(err)) {
        setNetworkError(true);
      } else {
        setNetworkError(false);
      }
      // Re-throw the error so callers can handle it if needed
      throw err;
    }
  };

  return (
    <UserContext.Provider value={{ 
      user, 
      loading: authInitializing || loading, 
      refetchUser, 
      setSignupFlowActive, 
      error,
      networkError 
    }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
} 