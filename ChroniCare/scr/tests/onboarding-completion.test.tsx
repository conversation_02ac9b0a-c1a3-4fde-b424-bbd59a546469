import { renderHook, act } from '@testing-library/react-native';
import { MockedProvider } from '@apollo/client/testing';
import { useCompleteOnboarding } from '../hooks/completeOnboarding';
import { COMPLETE_ONBOARDING } from '../graphql/queries';
import { useOnboardingStore } from '../utils/onboardingStore';
import { initializeApp } from '@react-native-firebase/app';
import auth from '@react-native-firebase/auth';

// Mock Firebase
jest.mock('@react-native-firebase/app', () => ({
  initializeApp: jest.fn(),
}));

jest.mock('@react-native-firebase/auth', () => ({
  default: jest.fn(() => ({
    createUserWithEmailAndPassword: jest.fn(),
    signOut: jest.fn(),
    currentUser: null,
  })),
}));

// Mock user context
jest.mock('../context/userContext', () => ({
  useUser: () => ({
    refetchUser: jest.fn(),
  }),
}));

// Mock auth context
jest.mock('../context/authContext', () => ({
  useAuth: () => ({
    signUp: jest.fn(),
  }),
}));

// Mock expo-haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
}));

describe('Onboarding Completion Flow', () => {
  const testEmail = '<EMAIL>';
  const testPassword = 'TestPassword123!';
  let testUserId: string;
  let firebaseUid: string;

  const disease1 = {
    id: 'Inflammatory Bowel Disease',
    label: 'IBD',
    icdCode: 'DD7Z',
  };
  const disease2 = {
    id: 'Crohn\'s Disease',
    label: 'Crohn\'s',
    icdCode: 'DD70',
  };
  const medication1 = {
    id: '5-ASA',
    label: '5-ASA',
  };
  const medication2 = {
    id: 'adalimumab',
    label: 'Remicade',
  };
  const medicalDevice1 = {
    id: 'adiposyn',
    label: 'Adiposyn',
  };

  // Test data that matches the onboarding store format
  const testOnboardingData = {
    consent: {
      dataPrivacy: true,
      dataSharing: true,
      marketing: false,
    },
    personalInfo: {
      firstName: 'John',
      lastName: 'Doe',
      birthdate: new Date('1990-01-01'),
      gender: 'male',
      countryCode: 'US',
      countryName: 'United States',
      language: 'en',
    },
    profilePicture: {
      imageUri: null,
    },
    diseases: {
      selectedDiseases: [
        {
          id: disease1.id,
          label: disease1.label,
          icdCode: disease1.icdCode,
        },
        {
          id: disease2.id,
          label: disease2.label,
          icdCode: disease2.icdCode,
        },
      ],
    },
    userTypes: {
      diseaseUserTypes: {
        [disease1.id]: {
          role: 'patient' as const,
          diagnosisStatus: 'diagnosed' as const,
          diagnosisDate: new Date('2020-01-01'),
        },
        [disease2.id]: {
          role: 'caregiver' as const,
        },
      },
    },
    medications: {
      diseaseRelatedMedications: {
        [disease1.id]: [
          {
            medication: {
              id: medication1.id,
              label: medication1.label,
            },
            dosage: '10mg',
            frequency: 'twice daily',
            notes: 'Take with food',
            startDate: new Date('2020-01-01'),
            isCurrent: true,
          },
        ],
      },
      unrelatedMedications: [
        {
          medication: {
            id: medication2.id,
            label: medication2.label,
          },
          dosage: '5mg',
          frequency: 'once daily',
          notes: 'Take before bed',
          startDate: new Date('2019-01-01'),
          isCurrent: true,
        },
      ],
      completedDiseases: new Set([disease1.id]),
    },
    medicalDevices: {
      diseaseRelatedMedicalDevices: {
        [disease1.id]: [
          {
            device: {
              id: medicalDevice1.id,
              label: medicalDevice1.label,
            },
          },
        ],
      },
    },
  };

  beforeAll(async () => {
    // Initialize Firebase for testing
    await initializeApp({
      apiKey: 'test-api-key',
      authDomain: 'test-domain',
      projectId: 'test-project',
      storageBucket: 'test-bucket',
      messagingSenderId: 'test-sender',
      appId: 'test-app-id',
    });
  });

  beforeEach(() => {
    // Reset the onboarding store before each test
    useOnboardingStore.getState().resetOnboarding();
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // Cleanup: Remove user from database if test failed
    if (testUserId) {
      try {
        const { exec } = require('child_process');
        await new Promise((resolve, reject) => {
          exec(`cd /Users/<USER>/Documents/codebase/backend && npm run script:cleanup-user ${testEmail}`, (error: Error | null, stdout: string, stderr: string) => {
            if (error) {
              console.warn('Database cleanup failed:', error);
              resolve(null);
            } else {
              console.log('Database cleanup completed:', stdout);
              resolve(stdout);
            }
          });
        });
      } catch (error) {
        console.warn('Database cleanup error:', error);
      }
    }

    // Cleanup: Remove user from Firebase
    if (firebaseUid) {
      try {
        const firebaseAuth = auth();
        if (firebaseAuth.currentUser) {
          await firebaseAuth.currentUser.delete();
        }
      } catch (error) {
        console.warn('Firebase cleanup failed:', error);
      }
    }
  });

  it('should complete the full onboarding flow', async () => {
    // Step 1: Create Firebase user
    const mockFirebaseUser = {
      uid: 'test-firebase-uid',
      email: testEmail,
      displayName: null,
      emailVerified: false,
      photoURL: null,
    };

    firebaseUid = mockFirebaseUser.uid;

    testUserId = 'test-user-id';

    // Step 2: Populate onboarding store with test data
    const store = useOnboardingStore.getState();
    
    // Update consent
    store.updateConsent(testOnboardingData.consent);
    store.setConsentComplete();
    
    // Update personal info
    store.updatePersonalInfo(testOnboardingData.personalInfo);
    store.setPersonalInfoComplete();
    
    // Update profile picture
    store.setProfilePicture(testOnboardingData.profilePicture.imageUri);
    store.setProfilePictureComplete();
    
    // Update diseases
    store.setSelectedDiseases(testOnboardingData.diseases.selectedDiseases);
    store.setDiseasesComplete();
    
    // Update user types
    Object.entries(testOnboardingData.userTypes.diseaseUserTypes).forEach(([diseaseId, userType]) => {
      store.setUserTypeForDisease(
        diseaseId,
        (userType as any).role,
        (userType as any).diagnosisStatus,
        (userType as any).diagnosisDate
      );
    });
    store.setUserTypesComplete();
    
    // Update medications
    Object.entries(testOnboardingData.medications.diseaseRelatedMedications).forEach(([diseaseId, medications]) => {
      medications.forEach(medication => {
        store.addMedicationToDisease(diseaseId, medication);
      });
    });
    
    testOnboardingData.medications.unrelatedMedications.forEach(medication => {
      store.addUnrelatedMedication(medication);
    });
    store.setMedicationsComplete();
    
    // Update medical devices
    Object.entries(testOnboardingData.medicalDevices.diseaseRelatedMedicalDevices).forEach(([diseaseId, devices]) => {
      devices.forEach(device => {
        store.addMedicalDeviceToDisease(diseaseId, device);
      });
    });

    // Step 3: Mock the complete onboarding mutation
    const mockCompleteOnboardingMutation = jest.fn().mockResolvedValue({
      data: {
        completeOnboarding: true,
      },
    });

    const mocks = [
      {
        request: {
          query: COMPLETE_ONBOARDING,
          variables: {
            data: {
              consent: testOnboardingData.consent,
              personalInfo: testOnboardingData.personalInfo,
              profilePicture: {
                imageUri: testOnboardingData.profilePicture.imageUri,
              },
              diseases: {
                selectedDiseases: testOnboardingData.diseases.selectedDiseases.map(({ id, icdCode }) => ({
                  id,
                  icdCode,
                })),
              },
              userTypes: {
                diseaseUserTypes: JSON.stringify(testOnboardingData.userTypes.diseaseUserTypes),
              },
              medications: {
                diseaseRelatedMedications: JSON.stringify(testOnboardingData.medications.diseaseRelatedMedications),
                unrelatedMedications: testOnboardingData.medications.unrelatedMedications.map(med => ({
                  ...med,
                  medication: {
                    id: med.medication.id,
                    label: med.medication.label
                  }
                })),
              },
              medicalDevices: {
                diseaseRelatedMedicalDevices: JSON.stringify(testOnboardingData.medicalDevices.diseaseRelatedMedicalDevices),
              },
            },
          },
        },
        result: {
          data: {
            completeOnboarding: true,
          },
        },
      },
    ];

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MockedProvider mocks={mocks} addTypename={false}>
        {children}
      </MockedProvider>
    );

    // Step 4: Test the onboarding completion hook
    const { result } = renderHook(() => useCompleteOnboarding(), { wrapper });

    // Step 5: Execute the complete onboarding function
    let completionResult: boolean | undefined;
    await act(async () => {
      completionResult = await result.current.completeOnboarding();
    });

    // Step 6: Verify the results
    expect(completionResult).toBe(true);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeFalsy();

    // Step 7: Verify the onboarding data structure
    const finalOnboardingData = useOnboardingStore.getState().getOnboardingData();
    
    // Verify consent data
    expect(finalOnboardingData.consent.dataPrivacy).toBe(true);
    expect(finalOnboardingData.consent.dataSharing).toBe(true);
    expect(finalOnboardingData.consent.marketing).toBe(false);
    
    // Verify personal info
    expect(finalOnboardingData.personalInfo.firstName).toBe(testOnboardingData.personalInfo.firstName);
    expect(finalOnboardingData.personalInfo.lastName).toBe(testOnboardingData.personalInfo.lastName);
    expect(finalOnboardingData.personalInfo.gender).toBe(testOnboardingData.personalInfo.gender);
    expect(finalOnboardingData.personalInfo.countryCode).toBe(testOnboardingData.personalInfo.countryCode);
    expect(finalOnboardingData.personalInfo.language).toBe(testOnboardingData.personalInfo.language);
    
    // Verify diseases
    expect(finalOnboardingData.diseases.selectedDiseases).toHaveLength(2);
    expect(finalOnboardingData.diseases.selectedDiseases[0].icdCode).toBe(testOnboardingData.diseases.selectedDiseases[0].icdCode);
    expect(finalOnboardingData.diseases.selectedDiseases[1].icdCode).toBe(testOnboardingData.diseases.selectedDiseases[1].icdCode);
    
    // Verify user types
    expect(finalOnboardingData.userTypes.diseaseUserTypes[disease1.id].role).toBe(testOnboardingData.userTypes.diseaseUserTypes[disease1.id].role);
    expect(finalOnboardingData.userTypes.diseaseUserTypes[disease1.id].diagnosisStatus).toBe(testOnboardingData.userTypes.diseaseUserTypes[disease1.id].diagnosisStatus);
    expect(finalOnboardingData.userTypes.diseaseUserTypes[disease2.id].role).toBe(testOnboardingData.userTypes.diseaseUserTypes[disease2.id].role);
    
    // Verify medications
    expect(finalOnboardingData.medications.diseaseRelatedMedications[disease1.id]).toHaveLength(1);
    expect(finalOnboardingData.medications.diseaseRelatedMedications[disease1.id][0].medication.label).toBe(medication1.label);
    expect(finalOnboardingData.medications.unrelatedMedications).toHaveLength(1);
    expect(finalOnboardingData.medications.unrelatedMedications[0].medication.label).toBe(medication2.label);
    
    // Verify medical devices
    expect(finalOnboardingData.medicalDevices.diseaseRelatedMedicalDevices[disease1.id]).toHaveLength(1);
    expect(finalOnboardingData.medicalDevices.diseaseRelatedMedicalDevices[disease1.id][0].device.label).toBe(medicalDevice1.label);
  });

  it('should handle onboarding completion failure gracefully', async () => {
    // Populate store with minimal data
    const store = useOnboardingStore.getState();
    store.updateConsent({ dataPrivacy: true, dataSharing: true, marketing: false });
    store.updatePersonalInfo({
      firstName: testOnboardingData.personalInfo.firstName,
      lastName: testOnboardingData.personalInfo.lastName,
      birthdate: testOnboardingData.personalInfo.birthdate,
      gender: testOnboardingData.personalInfo.gender,
      countryCode: testOnboardingData.personalInfo.countryCode,
      countryName: testOnboardingData.personalInfo.countryName,
      language: testOnboardingData.personalInfo.language,
    });

    // Mock a failed mutation
    const errorMocks = [
      {
        request: {
          query: COMPLETE_ONBOARDING,
        },
        error: new Error('Database error'),
      },
    ];

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MockedProvider mocks={errorMocks} addTypename={false}>
        {children}
      </MockedProvider>
    );

    const { result } = renderHook(() => useCompleteOnboarding(), { wrapper });

    let completionResult: boolean | undefined;
    await act(async () => {
      completionResult = await result.current.completeOnboarding();
    });

    expect(completionResult).toBe(false);
  });

  it('should validate required onboarding data before completion', async () => {
    // Test with incomplete data
    const store = useOnboardingStore.getState();
    
    // Only set consent, missing other required fields
    store.updateConsent({ dataPrivacy: true, dataSharing: true, marketing: false });
    
    // Verify validation methods work correctly
    expect(store.isConsentValid()).toBe(true);
    expect(store.isPersonalInfoValid()).toBe(false);
    expect(store.isDiseasesValid()).toBe(false);
    expect(store.areAllUserTypesComplete()).toBe(true); // No diseases selected, so technically complete
    
    // Test that we can't proceed to medications without completing earlier steps
    expect(store.canProceedToMedications()).toBe(false);
  });

  it('should correctly format data for backend consumption', async () => {
    // This test verifies the data transformation that happens in completeOnboarding hook
    const store = useOnboardingStore.getState();
    
    // Set up test data
    store.updateConsent(testOnboardingData.consent);
    store.updatePersonalInfo(testOnboardingData.personalInfo);
    store.setSelectedDiseases(testOnboardingData.diseases.selectedDiseases);
    
    // Get the formatted data
    const onboardingData = store.getOnboardingData();
    
    // Verify that diseases are properly formatted for backend (icdCode extraction)
    const cleanSelectedDiseases = onboardingData.diseases.selectedDiseases.map(({ id, icdCode }) => ({
      id,
      icdCode,
    }));
    
    expect(cleanSelectedDiseases).toEqual([
      { id: disease1.id, icdCode: disease1.icdCode },
      { id: disease2.id, icdCode: disease2.icdCode },
    ]);
    
    // Verify JSON stringification works for complex objects
    expect(() => JSON.stringify(onboardingData.userTypes.diseaseUserTypes)).not.toThrow();
    expect(() => JSON.stringify(onboardingData.medications.diseaseRelatedMedications)).not.toThrow();
    expect(() => JSON.stringify(onboardingData.medicalDevices.diseaseRelatedMedicalDevices)).not.toThrow();
  });
});