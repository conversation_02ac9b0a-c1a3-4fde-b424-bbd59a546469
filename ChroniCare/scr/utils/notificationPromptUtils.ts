import AsyncStorage from '@react-native-async-storage/async-storage';

const NOTIFICATIONS_PROMPT_KEY = 'hasSeenNotificationsPrompt';

/**
 * Utility functions for managing the notification prompt state
 */
export const notificationPromptUtils = {
  /**
   * Check if the user has seen the notification prompt
   */
  async hasSeenPrompt(): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(NOTIFICATIONS_PROMPT_KEY);
      return value === 'true';
    } catch (error) {
      console.error('Error checking notification prompt status:', error);
      return false;
    }
  },

  /**
   * Mark the notification prompt as seen
   */
  async markPromptAsSeen(): Promise<void> {
    try {
      await AsyncStorage.setItem(NOTIFICATIONS_PROMPT_KEY, 'true');
    } catch (error) {
      console.error('Error marking notification prompt as seen:', error);
      throw error;
    }
  },

  /**
   * Reset the notification prompt (useful for testing)
   */
  async resetPrompt(): Promise<void> {
    try {
      await AsyncStorage.removeItem(NOTIFICATIONS_PROMPT_KEY);
    } catch (error) {
      console.error('Error resetting notification prompt:', error);
      throw error;
    }
  },

  /**
   * Get the storage key (useful for debugging)
   */
  getStorageKey(): string {
    return NOTIFICATIONS_PROMPT_KEY;
  }
};
